{
  "name": "adib-pdv",
  "version": "1.0.0",
  "description": "Sistema PDV offline-first com Electron, React, Node.js e integração fiscal",
  "main": "frontend/public/electron.js",
  "private": true,
  "workspaces": [
    "frontend",
    "backend",
    "fiscal"
  ],
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run dev:fiscal\"",
    "dev:frontend": "cd frontend && npm run dev",
    "dev:backend": "cd backend && npm run dev",
    "dev:fiscal": "cd fiscal && python -m uvicorn src.main:app --reload --port 8001",
    "build": "npm run build:backend && npm run build:frontend",
    "build:frontend": "cd frontend && npm run build",
    "build:backend": "cd backend && npm run build",
    "electron": "cd frontend && npm run electron",
    "electron:dev": "cd frontend && npm run electron:dev",
    "dist": "cd frontend && npm run dist",
    "test": "npm run test:backend && npm run test:frontend",
    "test:frontend": "cd frontend && npm test",
    "test:backend": "cd backend && npm test",
    "lint": "npm run lint:frontend && npm run lint:backend",
    "lint:frontend": "cd frontend && npm run lint",
    "lint:backend": "cd backend && npm run lint",
    "setup": "npm install && npm run setup:frontend && npm run setup:backend && npm run setup:fiscal",
    "setup:frontend": "cd frontend && npm install",
    "setup:backend": "cd backend && npm install",
    "setup:fiscal": "cd fiscal && pip install -r requirements.txt",
    "db:migrate": "cd backend && npm run db:migrate",
    "db:seed": "cd backend && npm run db:seed",
    "db:reset": "cd backend && npm run db:reset",
    "db:init": "node scripts/init-db.js",

  },
  "devDependencies": {
    "concurrently": "^8.2.2",
    "cross-env": "^7.0.3",
    "fork-ts-checker-webpack-plugin": "^6.5.3",
    "lodash": "^4.17.21",
    "wait-on": "^7.2.0"
  },
  "engines": {
    "node": ">=18.0.0",
    "npm": ">=9.0.0"
  },
  "repository": {
    "type": "git",
    "url": "https://github.com/seu-usuario/adib-pdv.git"
  },
  "keywords": [
    "pdv",
    "pos",
    "electron",
    "react",
    "nodejs",
    "sqlite",
    "fiscal",
    "tef",
    "offline-first"
  ],
  "author": "Adib PDV Team",
  "license": "MIT",
  "dependencies": {
    "bcryptjs": "^3.0.2",
    "cors": "^2.8.5",
    "express": "^5.1.0",
    "helmet": "^8.1.0",
    "jsonwebtoken": "^9.0.2",
    "morgan": "^1.10.0",
    "sqlite3": "^5.1.7"
  }
}

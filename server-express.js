const express = require('express');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Dados mock para teste
const mockData = {
  users: [
    { id: 1, username: 'admin', password: 'password', role: 'manager', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 2, username: 'caix<PERSON>', password: 'password', role: 'cashier', name: '<PERSON><PERSON> de Caixa' },
    { id: 3, username: 'co<PERSON><PERSON>', password: 'password', role: 'kitchen', name: '<PERSON><PERSON><PERSON>' }
  ],
  products: [
    { id: 1, name: 'Hambúrguer Clássico', price: 25.90, category: 'Hambúrgueres', active: true },
    { id: 2, name: 'Pizza Margherita', price: 35.00, category: 'Pizzas', active: true },
    { id: 3, name: 'Refrigerante Lata', price: 5.50, category: 'Bebidas', active: true },
    { id: 4, name: 'Batata Frita', price: 12.00, category: 'Acompanhamentos', active: true },
    { id: 5, name: 'Sorvete', price: 8.50, category: 'Sobremesas', active: true }
  ],
  orders: []
};

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Logs
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Rota principal - servir HTML
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API Routes
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Sistema PDV Adib funcionando!',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    status: 'online'
  });
});

app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    data: {
      system: 'PDV Adib',
      version: '1.0.0',
      environment: 'development',
      features: [
        'PDV (Ponto de Venda)',
        'Sistema de Cozinha (KDS)',
        'Gestão de Produtos',
        'Relatórios e Analytics',
        'TEF',
        'Offline-First',
        'Sincronização'
      ],
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    }
  });
});

app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  if (!username || !password) {
    return res.status(400).json({
      success: false,
      error: 'Username e password são obrigatórios'
    });
  }
  
  const user = mockData.users.find(u => u.username === username && u.password === password);
  
  if (user) {
    res.json({
      success: true,
      data: {
        token: 'demo-token-' + Date.now(),
        user: {
          id: user.id,
          username: user.username,
          name: user.name,
          role: user.role
        }
      },
      message: 'Login realizado com sucesso!'
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Credenciais inválidas'
    });
  }
});

app.get('/api/products', (req, res) => {
  res.json({
    success: true,
    data: mockData.products.filter(p => p.active),
    total: mockData.products.filter(p => p.active).length
  });
});

app.get('/api/orders', (req, res) => {
  res.json({
    success: true,
    data: mockData.orders,
    total: mockData.orders.length
  });
});

app.post('/api/orders', (req, res) => {
  const { items, total } = req.body;
  
  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Itens são obrigatórios'
    });
  }
  
  const order = {
    id: mockData.orders.length + 1,
    number: 'PED' + String(mockData.orders.length + 1).padStart(3, '0'),
    status: 'pending',
    items,
    total: total || items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
    created_at: new Date().toISOString()
  };
  
  mockData.orders.push(order);
  
  res.json({
    success: true,
    data: order,
    message: 'Pedido criado com sucesso!'
  });
});

app.get('/api/sync/metrics', (req, res) => {
  res.json({
    success: true,
    data: {
      lastSync: new Date().toISOString(),
      pendingItems: 0,
      syncStatus: 'up_to_date',
      conflicts: 0,
      totalSynced: 150
    }
  });
});

app.get('/api/reports/sales-summary', (req, res) => {
  const totalSales = mockData.orders.reduce((sum, order) => sum + order.total, 0);
  const totalOrders = mockData.orders.length;
  const averageTicket = totalOrders > 0 ? totalSales / totalOrders : 0;
  
  res.json({
    success: true,
    data: {
      totalSales,
      totalOrders,
      averageTicket,
      topProducts: mockData.products.slice(0, 3),
      period: 'today'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint não encontrado',
    message: `${req.method} ${req.originalUrl} não existe`
  });
});

// Error handler
app.use((err, req, res, next) => {
  console.error('Erro:', err);
  res.status(500).json({
    success: false,
    error: 'Erro interno do servidor',
    message: err.message
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log('\n🚀 Sistema PDV Adib - Interface Web com Express!');
  console.log(`📡 Servidor rodando em: http://localhost:${PORT}`);
  console.log(`🌐 Interface Web: http://localhost:${PORT}`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Status: http://localhost:${PORT}/api/status`);
  console.log('\n👤 Credenciais de teste:');
  console.log('   Admin: admin / password');
  console.log('   Caixa: caixa / password');
  console.log('   Cozinha: cozinha / password');
  console.log('\n✨ Sistema com interface gráfica pronto!');
  console.log('\n🌐 Abra no navegador: http://localhost:' + PORT);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🔄 Encerrando servidor...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('\n🔄 Encerrando servidor...');
  process.exit(0);
});

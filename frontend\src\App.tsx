import { Box, CircularProgress, createTheme, CssBaseline, ThemeProvider } from '@mui/material';
import React, { useEffect } from 'react';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';

// Contextos e hooks
import { useAuthStore } from './services/auth';

// Páginas
import Cashier from './pages/Cashier';
import Dashboard from './pages/Dashboard';
import Fiscal from './pages/Fiscal';
import Kitchen from './pages/Kitchen';
import Login from './pages/Login';
import POS from './pages/POS';
import Products from './pages/Products';
import Reports from './pages/Reports';
import Settings from './pages/Settings';
import Sync from './pages/Sync';
import Tef from './pages/Tef';

// Componentes
import ErrorBoundary from './components/ErrorBoundary';
import MainLayout from './components/Layout/MainLayout';
import ProtectedRoute from './components/ProtectedRoute';

// Tema personalizado - Meta-style com cores do projeto
const theme = createTheme({
  palette: {
    primary: {
      main: '#0070F3', // Azul principal
      light: '#4A9EFF',
      dark: '#0056CC',
    },
    secondary: {
      main: '#37ECC8', // Turquesa
      light: '#6BFFD8',
      dark: '#00B894',
    },
    warning: {
      main: '#FFBA08', // Amber
      light: '#FFD93D',
      dark: '#CC9500',
    },
    background: {
      default: '#F5F5F5', // Cinza claro
      paper: '#FFFFFF',
    },
    text: {
      primary: '#0A0F24', // Navy escuro
      secondary: '#6B7280',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
    },
    h2: {
      fontWeight: 600,
    },
    h3: {
      fontWeight: 600,
    },
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          borderRadius: 12,
          padding: '10px 24px',
          fontSize: '0.95rem',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(0, 112, 243, 0.15)',
          },
        },
        contained: {
          background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #0056CC 0%, #00B894 100%)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          borderRadius: 16,
          border: '1px solid rgba(0,0,0,0.05)',
          '&:hover': {
            boxShadow: '0 8px 30px rgba(0,0,0,0.12)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
          boxShadow: '0 2px 20px rgba(0, 112, 243, 0.15)',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
        },
      },
    },
  },
});

// Componente de loading moderno
const LoadingScreen: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      gap: 3,
      background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
      color: 'white',
    }}
  >
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        gap: 2,
        padding: 4,
        borderRadius: 3,
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
      }}
    >
      <Box
        sx={{
          fontSize: '2.5rem',
          fontWeight: 700,
          marginBottom: 1,
          background: 'linear-gradient(45deg, #FFFFFF 30%, #37ECC8 90%)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
        }}
      >
        Adib PDV
      </Box>
      <CircularProgress
        size={50}
        sx={{
          color: 'white',
          '& .MuiCircularProgress-circle': {
            strokeLinecap: 'round',
          }
        }}
      />
      <Box sx={{
        textAlign: 'center',
        fontSize: '1.1rem',
        fontWeight: 500,
        opacity: 0.9
      }}>
        Carregando sistema...
      </Box>
    </Box>
  </Box>
);

// Hook para verificar autenticação na inicialização
const useInitialAuth = () => {
  const { checkAuth, isAuthenticated, isLoading } = useAuthStore();
  const [initializing, setInitializing] = React.useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await checkAuth();
      } catch (error) {
        console.error('Initial auth check failed:', error);
      } finally {
        setInitializing(false);
      }
    };

    initAuth();
  }, [checkAuth]);

  return { initializing, isAuthenticated, isLoading };
};

const App: React.FC = () => {
  const { initializing, isAuthenticated, isLoading } = useInitialAuth();

  // Mostrar loading durante inicialização
  if (initializing || isLoading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <LoadingScreen />
      </ThemeProvider>
    );
  }

  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            {/* Rota de login */}
            <Route
              path="/login"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <Login />
                )
              }
            />

            {/* Rotas protegidas com layout */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <MainLayout>
                    <Dashboard />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/products"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <MainLayout>
                    <Products />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/pos"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <MainLayout>
                    <POS />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/kitchen"
              element={
                <ProtectedRoute roles={['manager', 'kitchen']}>
                  <MainLayout>
                    <Kitchen />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/cashier"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <MainLayout>
                    <Cashier />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/fiscal"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <MainLayout>
                    <Fiscal />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/tef"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <MainLayout>
                    <Tef />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/reports"
              element={
                <ProtectedRoute roles={['manager']}>
                  <MainLayout>
                    <Reports />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/sync"
              element={
                <ProtectedRoute roles={['manager']}>
                  <MainLayout>
                    <Sync />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/settings"
              element={
                <ProtectedRoute roles={['manager']}>
                  <MainLayout>
                    <Settings />
                  </MainLayout>
                </ProtectedRoute>
              }
            />

            {/* Rota padrão */}
            <Route
              path="/"
              element={
                <Navigate
                  to={isAuthenticated ? "/dashboard" : "/login"}
                  replace
                />
              }
            />

            {/* Rota 404 */}
            <Route
              path="*"
              element={
                <Navigate
                  to={isAuthenticated ? "/dashboard" : "/login"}
                  replace
                />
              }
            />
          </Routes>
        </Router>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;

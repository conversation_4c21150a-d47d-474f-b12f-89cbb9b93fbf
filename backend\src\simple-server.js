const express = require('express');
const cors = require('cors');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Conectar ao banco de dados
const dbPath = path.join(__dirname, '..', '..', '..', 'database', 'adib.db');
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('❌ Database connection failed:', err.message);
  } else {
    console.log('✅ Database connected successfully');
  }
});

// Rota de health check
app.get('/api/health', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: 'development',
    },
  });
});

// Rota de login (mock)
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  // Usuários mock
  const mockUsers = [
    { username: 'admin', password: 'password', role: 'manager', full_name: 'Administrador' },
    { username: 'caixa', password: 'password', role: 'cashier', full_name: 'Operador de Caixa' },
    { username: 'cozinha', password: 'password', role: 'kitchen', full_name: 'Cozinheiro' },
  ];

  const user = mockUsers.find(u => u.username === username && u.password === password);

  if (!user) {
    return res.status(401).json({
      success: false,
      error: 'Credenciais inválidas',
      timestamp: new Date().toISOString(),
    });
  }

  const mockToken = `mock_token_${Date.now()}`;
  const mockUser = {
    id: 1,
    username: user.username,
    full_name: user.full_name,
    email: `${user.username}@adib.com`,
    role: user.role,
    is_active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };

  res.status(200).json({
    success: true,
    data: {
      user: mockUser,
      token: mockToken,
      expires_in: '24h',
    },
  });
});

// Rota de logout
app.post('/api/auth/logout', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      message: 'Logout realizado com sucesso',
    },
  });
});

// Rota de produtos
app.get('/api/products', (req, res) => {
  db.all('SELECT * FROM products WHERE active = 1', [], (err, rows) => {
    if (err) {
      return res.status(500).json({
        success: false,
        error: 'Erro ao buscar produtos',
        timestamp: new Date().toISOString(),
      });
    }

    res.status(200).json({
      success: true,
      data: rows || [],
      timestamp: new Date().toISOString(),
    });
  });
});

// Rota de categorias
app.get('/api/products/categories', (req, res) => {
  db.all('SELECT * FROM categories WHERE active = 1', [], (err, rows) => {
    if (err) {
      return res.status(500).json({
        success: false,
        error: 'Erro ao buscar categorias',
        timestamp: new Date().toISOString(),
      });
    }

    res.status(200).json({
      success: true,
      data: rows || [],
      timestamp: new Date().toISOString(),
    });
  });
});

// Rota raiz da API
app.get('/api', (req, res) => {
  res.status(200).json({
    success: true,
    data: {
      message: 'Adib PDV Backend API',
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      endpoints: {
        auth: '/api/auth',
        health: '/api/health',
        products: '/api/products',
      },
    },
  });
});

// Middleware para rotas não encontradas
app.use('/api/*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found',
    timestamp: new Date().toISOString(),
  });
});

// Middleware global de tratamento de erros
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  res.status(error.statusCode || 500).json({
    success: false,
    error: error.message || 'Internal server error',
    timestamp: new Date().toISOString(),
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log(`🚀 Server running on http://localhost:${PORT}`);
  console.log(`📚 API Documentation: http://localhost:${PORT}/api`);
  console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`🔐 Auth Endpoints: http://localhost:${PORT}/api/auth`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n📴 Shutting down server...');
  db.close((err) => {
    if (err) {
      console.error('❌ Error closing database:', err.message);
    } else {
      console.log('🗄️  Database connection closed');
    }
    process.exit(0);
  });
});

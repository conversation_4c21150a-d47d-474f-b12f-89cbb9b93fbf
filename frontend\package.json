{"name": "adib-pdv-frontend", "version": "1.0.0", "description": "Frontend do Sistema PDV Adib - Electron + React", "main": "public/electron.js", "private": true, "scripts": {"dev": "react-scripts start", "start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "npx electron .", "electron:dev": "echo 'Execute em terminais separados: npm run dev (em um terminal) e npm run electron (em outro terminal)'", "electron:pack": "electron-builder", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "axios": "^1.6.2", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "^5.0.1", "socket.io-client": "^4.7.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.4", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.8.1", "typescript": "^4.9.5"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.adib.pdv", "productName": "Adib PDV", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "node_modules/**/*"], "win": {"target": "nsis"}, "linux": {"target": "deb", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}
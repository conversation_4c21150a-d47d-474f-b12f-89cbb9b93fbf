import { CssBaseline } from '@mui/material';
import { createTheme, ThemeProvider as MuiThemeProvider, Theme } from '@mui/material/styles';
import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';

interface ThemeContextType {
  isDarkMode: boolean;
  toggleTheme: () => void;
  theme: Theme;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Verificar preferência salva ou preferência do sistema
  const getInitialTheme = (): boolean => {
    const saved = localStorage.getItem('theme-mode');
    if (saved) {
      return saved === 'dark';
    }
    // Usar preferência do sistema
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  };

  const [isDarkMode, setIsDarkMode] = useState(getInitialTheme);

  // Tema claro - Paleta Meta
  const lightTheme = createTheme({
    palette: {
      mode: 'light',
      primary: {
        main: '#0070F3', // Azul Elétrico
        light: '#4A9EFF',
        dark: '#0056B3',
        contrastText: '#ffffff',
      },
      secondary: {
        main: '#37ECC8', // Turquesa Vivo
        light: '#6BFFD9',
        dark: '#00C4A7',
        contrastText: '#0A0F24',
      },
      background: {
        default: '#F5F5F5', // Cinza Suave
        paper: '#ffffff',
      },
      text: {
        primary: '#0A0F24', // Azul-Marinho
        secondary: '#6B7280',
      },
      success: {
        main: '#37ECC8', // Turquesa Vivo
        light: '#6BFFD9',
        dark: '#00C4A7',
      },
      warning: {
        main: '#FFBA08', // Amarelo Âmbar
        light: '#FFD93D',
        dark: '#E6A500',
        contrastText: '#0A0F24',
      },
      error: {
        main: '#EF4444',
        light: '#F87171',
        dark: '#DC2626',
      },
      info: {
        main: '#0070F3', // Azul Elétrico
        light: '#4A9EFF',
        dark: '#0056B3',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2.5rem',
        fontWeight: 500,
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 500,
      },
      h3: {
        fontSize: '1.75rem',
        fontWeight: 500,
      },
      h4: {
        fontSize: '1.5rem',
        fontWeight: 500,
      },
      h5: {
        fontSize: '1.25rem',
        fontWeight: 500,
      },
      h6: {
        fontSize: '1rem',
        fontWeight: 500,
      },
    },
    shape: {
      borderRadius: 12,
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none',
            borderRadius: 8,
            fontWeight: 500,
            padding: '10px 24px',
            fontSize: '0.95rem',
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 4px 12px rgba(0, 112, 243, 0.25)',
            },
          },
          contained: {
            background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #0056B3 0%, #00C4A7 100%)',
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
            borderRadius: 16,
            border: '1px solid rgba(0, 0, 0, 0.05)',
            transition: 'all 0.3s ease',
            '&:hover': {
              boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
              transform: 'translateY(-2px)',
            },
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 12,
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#0070F3',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#0070F3',
                borderWidth: 2,
              },
            },
          },
        },
      },
    },
  });

  // Tema escuro - Paleta Meta
  const darkTheme = createTheme({
    palette: {
      mode: 'dark',
      primary: {
        main: '#4A9EFF', // Azul Elétrico mais claro para dark mode
        light: '#7BB8FF',
        dark: '#0070F3',
        contrastText: '#0A0F24',
      },
      secondary: {
        main: '#37ECC8', // Turquesa Vivo
        light: '#6BFFD9',
        dark: '#00C4A7',
        contrastText: '#0A0F24',
      },
      background: {
        default: '#0A0F24', // Azul-Marinho
        paper: '#1A1F35',
      },
      text: {
        primary: '#F5F5F5', // Cinza Suave
        secondary: '#B0B7C3',
      },
      success: {
        main: '#37ECC8', // Turquesa Vivo
        light: '#6BFFD9',
        dark: '#00C4A7',
      },
      warning: {
        main: '#FFBA08', // Amarelo Âmbar
        light: '#FFD93D',
        dark: '#E6A500',
        contrastText: '#0A0F24',
      },
      error: {
        main: '#F87171',
        light: '#FCA5A5',
        dark: '#EF4444',
      },
      info: {
        main: '#4A9EFF', // Azul Elétrico mais claro
        light: '#7BB8FF',
        dark: '#0070F3',
      },
    },
    typography: {
      fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2.5rem',
        fontWeight: 500,
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 500,
      },
      h3: {
        fontSize: '1.75rem',
        fontWeight: 500,
      },
      h4: {
        fontSize: '1.5rem',
        fontWeight: 500,
      },
      h5: {
        fontSize: '1.25rem',
        fontWeight: 500,
      },
      h6: {
        fontSize: '1rem',
        fontWeight: 500,
      },
    },
    shape: {
      borderRadius: 12,
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none',
            borderRadius: 8,
            fontWeight: 500,
            padding: '10px 24px',
            fontSize: '0.95rem',
            boxShadow: 'none',
            '&:hover': {
              boxShadow: '0 4px 12px rgba(74, 158, 255, 0.25)',
            },
          },
          contained: {
            background: 'linear-gradient(135deg, #4A9EFF 0%, #37ECC8 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #0070F3 0%, #00C4A7 100%)',
            },
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
            borderRadius: 16,
            backgroundColor: '#1A1F35',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              boxShadow: '0 8px 24px rgba(0, 0, 0, 0.4)',
              transform: 'translateY(-2px)',
            },
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
            background: 'linear-gradient(135deg, #4A9EFF 0%, #37ECC8 100%)',
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundColor: '#1A1F35',
            border: '1px solid rgba(255, 255, 255, 0.1)',
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            fontWeight: 500,
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 12,
              '&:hover .MuiOutlinedInput-notchedOutline': {
                borderColor: '#4A9EFF',
              },
              '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                borderColor: '#4A9EFF',
                borderWidth: 2,
              },
            },
          },
        },
      },
    },
  });

  const theme = isDarkMode ? darkTheme : lightTheme;

  const toggleTheme = () => {
    const newMode = !isDarkMode;
    setIsDarkMode(newMode);
    localStorage.setItem('theme-mode', newMode ? 'dark' : 'light');
  };

  // Escutar mudanças na preferência do sistema
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent) => {
      // Só mudar se não houver preferência salva
      if (!localStorage.getItem('theme-mode')) {
        setIsDarkMode(e.matches);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  // Escutar evento customizado para toggle
  useEffect(() => {
    const handleToggle = () => toggleTheme();
    document.addEventListener('theme:toggle', handleToggle);
    return () => document.removeEventListener('theme:toggle', handleToggle);
  }, [isDarkMode]);

  // Aplicar classe no body para CSS customizado
  useEffect(() => {
    document.body.className = isDarkMode ? 'dark-theme' : 'light-theme';
  }, [isDarkMode]);

  const value: ThemeContextType = {
    isDarkMode,
    toggleTheme,
    theme,
  };

  return (
    <ThemeContext.Provider value={value}>
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

import { Close } from '@mui/icons-material';
import {
    <PERSON>ert,
    AlertColor,
    IconButton,
    Slide,
    SlideProps,
    Snackbar,
} from '@mui/material';
import React, { createContext, ReactNode, useCallback, useContext, useState } from 'react';

interface Notification {
  id: string;
  message: string;
  type: AlertColor;
  duration?: number;
  action?: ReactNode;
  persistent?: boolean;
}

interface NotificationContextType {
  showNotification: (
    message: string,
    type?: AlertColor,
    duration?: number,
    action?: ReactNode,
    persistent?: boolean
  ) => void;
  showSuccess: (message: string, duration?: number) => void;
  showError: (message: string, duration?: number) => void;
  showWarning: (message: string, duration?: number) => void;
  showInfo: (message: string, duration?: number) => void;
  hideNotification: (id: string) => void;
  hideAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

// Componente de transição
function SlideTransition(props: SlideProps) {
  return <Slide {...props} direction="up" />;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Gerar ID único
  const generateId = useCallback((): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }, []);

  // Mostrar notificação
  const showNotification = useCallback((
    message: string,
    type: AlertColor = 'info',
    duration: number = 6000,
    action?: ReactNode,
    persistent: boolean = false
  ) => {
    const id = generateId();
    const notification: Notification = {
      id,
      message,
      type,
      duration: persistent ? undefined : duration,
      action,
      persistent,
    };

    setNotifications(prev => [...prev, notification]);

    // Auto-remover se não for persistente
    if (!persistent && duration > 0) {
      setTimeout(() => {
        hideNotification(id);
      }, duration);
    }
  }, [generateId]);

  // Métodos de conveniência
  const showSuccess = useCallback((message: string, duration: number = 4000) => {
    showNotification(message, 'success', duration);
  }, [showNotification]);

  const showError = useCallback((message: string, duration: number = 8000) => {
    showNotification(message, 'error', duration);
  }, [showNotification]);

  const showWarning = useCallback((message: string, duration: number = 6000) => {
    showNotification(message, 'warning', duration);
  }, [showNotification]);

  const showInfo = useCallback((message: string, duration: number = 6000) => {
    showNotification(message, 'info', duration);
  }, [showNotification]);

  // Esconder notificação específica
  const hideNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  }, []);

  // Esconder todas as notificações
  const hideAllNotifications = useCallback(() => {
    setNotifications([]);
  }, []);

  const value: NotificationContextType = {
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideNotification,
    hideAllNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}

      {/* Renderizar notificações */}
      {notifications.map((notification, index) => (
        <Snackbar
          key={notification.id}
          open={true}
          autoHideDuration={notification.duration}
          onClose={() => hideNotification(notification.id)}
          TransitionComponent={SlideTransition}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          sx={{
            // Empilhar notificações
            bottom: `${16 + index * 70}px !important`,
            zIndex: 9999 + index,
          }}
        >
          <Alert
            severity={notification.type}
            variant="filled"
            onClose={() => hideNotification(notification.id)}
            action={
              notification.action || (
                notification.persistent && (
                  <IconButton
                    size="small"
                    aria-label="close"
                    color="inherit"
                    onClick={() => hideNotification(notification.id)}
                  >
                    <Close fontSize="small" />
                  </IconButton>
                )
              )
            }
            sx={{
              minWidth: 300,
              maxWidth: 500,
              boxShadow: 3,
            }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      ))}
    </NotificationContext.Provider>
  );
};

// Hook para usar notificações
export const useNotification = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

// Hook para notificações de operações assíncronas
export const useAsyncNotification = () => {
  const { showSuccess, showError, showInfo } = useNotification();

  const notifyAsync = useCallback(
    async (
      promise: Promise<any>,
      messages: {
        loading?: string;
        success?: string | ((result: any) => string);
        error?: string | ((error: any) => string);
      } = {}
    ) => {
      const loadingId = messages.loading ? Date.now().toString() : null;

      if (messages.loading && loadingId) {
        showInfo(messages.loading, 0); // Duração 0 = persistente
      }

      try {
        const result = await promise;

        if (loadingId) {
          // Esconder loading (implementar se necessário)
        }

        if (messages.success) {
          const successMessage = typeof messages.success === 'function'
            ? messages.success(result)
            : messages.success;
          showSuccess(successMessage);
        }

        return result;
      } catch (error) {
        if (loadingId) {
          // Esconder loading (implementar se necessário)
        }

        if (messages.error) {
          const errorMessage = typeof messages.error === 'function'
            ? messages.error(error)
            : messages.error;
          showError(errorMessage);
        }

        throw error;
      }
    },
    [showSuccess, showError, showInfo]
  );

  return { notifyAsync };
};

// Hook para notificações de ações do usuário
export const useActionNotification = () => {
  const { showSuccess, showError, showWarning } = useNotification();

  const notifyAction = useCallback((
    action: string,
    success: boolean,
    details?: string
  ) => {
    if (success) {
      showSuccess(`${action} realizada com sucesso${details ? `: ${details}` : ''}`);
    } else {
      showError(`Falha ao ${action.toLowerCase()}${details ? `: ${details}` : ''}`);
    }
  }, [showSuccess, showError]);

  const notifyValidation = useCallback((
    field: string,
    message: string
  ) => {
    showWarning(`${field}: ${message}`);
  }, [showWarning]);

  return { notifyAction, notifyValidation };
};

// Hook para notificações de sistema
export const useSystemNotification = () => {
  const { showInfo, showWarning, showError, showSuccess } = useNotification();

  const notifyConnection = useCallback((online: boolean) => {
    if (online) {
      showInfo('Conexão restaurada', 3000);
    } else {
      showWarning('Conexão perdida - modo offline ativado', 0); // Persistente
    }
  }, [showInfo, showWarning]);

  const notifySync = useCallback((status: 'started' | 'completed' | 'failed', details?: string) => {
    switch (status) {
      case 'started':
        showInfo('Sincronização iniciada...');
        break;
      case 'completed':
        showSuccess(`Sincronização concluída${details ? `: ${details}` : ''}`);
        break;
      case 'failed':
        showError(`Falha na sincronização${details ? `: ${details}` : ''}`);
        break;
    }
  }, [showInfo, showSuccess, showError]);

  const notifyUpdate = useCallback((version: string) => {
    showInfo(`Nova versão disponível: ${version}`, 10000);
  }, [showInfo]);

  return { notifyConnection, notifySync, notifyUpdate };
};

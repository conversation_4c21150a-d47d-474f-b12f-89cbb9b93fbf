import {
    Assessment,
    AttachMoney,
    Inventory,
    Settings,
    ShoppingCart,
    Store,
    TrendingUp
} from '@mui/icons-material';
import {
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    CircularProgress,
    Grid,
    Paper,
    Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../services/auth';
import { mockApi } from '../services/mockData';

interface DashboardStats {
  totalSales: number;
  totalOrders: number;
  averageTicket: number;
  activeOrders: number;
  cashBalance: number;
}

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const statsData = await mockApi.getDashboardStats();
        setDashboardStats(statsData);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  const statsCards = dashboardStats ? [
    {
      title: 'Vendas Hoje',
      value: `R$ ${dashboardStats.totalSales.toFixed(2).replace('.', ',')}`,
      icon: <AttachMoney />,
      gradient: 'linear-gradient(135deg, #37ECC8 0%, #00B894 100%)',
    },
    {
      title: 'Pedidos',
      value: dashboardStats.totalOrders.toString(),
      icon: <ShoppingCart />,
      gradient: 'linear-gradient(135deg, #0070F3 0%, #0056CC 100%)',
    },
    {
      title: 'Ticket Médio',
      value: `R$ ${dashboardStats.averageTicket.toFixed(2).replace('.', ',')}`,
      icon: <Assessment />,
      gradient: 'linear-gradient(135deg, #FFBA08 0%, #CC9500 100%)',
    },
    {
      title: 'Pedidos Ativos',
      value: dashboardStats.activeOrders.toString(),
      icon: <TrendingUp />,
      gradient: 'linear-gradient(135deg, #0A0F24 0%, #1A1F3A 100%)',
    },
  ] : [];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Paper
        sx={{
          p: 4,
          mb: 4,
          background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
          color: 'white',
          borderRadius: 3,
        }}
      >
        <Typography variant="h3" gutterBottom sx={{ fontWeight: 700, mb: 2 }}>
          Bem-vindo, {user?.full_name}!
        </Typography>
        <Typography variant="h6" sx={{ opacity: 0.9, fontWeight: 400 }}>
          Sistema PDV Adib funcionando perfeitamente.
        </Typography>
      </Paper>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        {statsCards.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                height: '100%',
                background: stat.gradient,
                color: 'white',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 40px rgba(0,0,0,0.15)',
                }
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box>
                    <Typography variant="h3" component="div" sx={{ fontWeight: 700, mb: 1 }}>
                      {stat.value}
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9, fontWeight: 500 }}>
                      {stat.title}
                    </Typography>
                  </Box>
                  <Avatar
                    sx={{
                      bgcolor: 'rgba(255, 255, 255, 0.2)',
                      width: 56,
                      height: 56,
                    }}
                  >
                    {stat.icon}
                  </Avatar>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Ações Rápidas
              </Typography>
              <Grid container spacing={2}>
                {user?.role === 'manager' && (
                  <>
                    <Grid item xs={12} sm={6} md={4}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<Inventory />}
                        sx={{ height: 60 }}
                        onClick={() => navigate('/products')}
                      >
                        Gerenciar Produtos
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<TrendingUp />}
                        sx={{ height: 60 }}
                        onClick={() => navigate('/reports')}
                      >
                        Relatórios
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<Settings />}
                        sx={{ height: 60 }}
                        onClick={() => navigate('/settings')}
                      >
                        Configurações
                      </Button>
                    </Grid>
                  </>
                )}

                {(user?.role === 'cashier' || user?.role === 'manager') && (
                  <>
                    <Grid item xs={12} sm={6} md={4}>
                      <Button
                        fullWidth
                        variant="contained"
                        startIcon={<ShoppingCart />}
                        sx={{ height: 60 }}
                        onClick={() => navigate('/pos')}
                      >
                        Novo Pedido
                      </Button>
                    </Grid>
                    <Grid item xs={12} sm={6} md={4}>
                      <Button
                        fullWidth
                        variant="outlined"
                        startIcon={<AttachMoney />}
                        sx={{ height: 60 }}
                        onClick={() => navigate('/cashier')}
                      >
                        Controle de Caixa
                      </Button>
                    </Grid>
                  </>
                )}

                {user?.role === 'kitchen' && (
                  <Grid item xs={12}>
                    <Button
                      fullWidth
                      variant="contained"
                      startIcon={<Store />}
                      sx={{ height: 60 }}
                      onClick={() => navigate('/kitchen')}
                    >
                      Visualizar Pedidos da Cozinha
                    </Button>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Status do Sistema
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Banco de Dados:</Typography>
                  <Chip label="Online" color="success" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Fiscal:</Typography>
                  <Chip label="Homologação" color="warning" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">TEF:</Typography>
                  <Chip label="Desabilitado" color="default" size="small" />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2">Backup:</Typography>
                  <Chip label="Ativo" color="success" size="small" />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;

import {
    Computer,
    Lock,
    Person,
    Store,
    Visibility,
    VisibilityOff,
} from '@mui/icons-material';
import {
    <PERSON>ert,
    Box,
    Button,
    Card,
    CardContent,
    CircularProgress,
    Divider,
    IconButton,
    InputAdornment,
    Paper,
    TextField,
    Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { useAuthStore } from '../services/auth';
import { LoginRequest, UserRole } from '../types';

const Login: React.FC = () => {
  const [formData, setFormData] = useState<LoginRequest>({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole | ''>('');

  const { login, isLoading, error, clearError, isAuthenticated } = useAuthStore();

  // Credenciais pré-definidas para facilitar o teste
  const predefinedUsers = [
    { username: 'admin', password: 'password', role: 'manager' as User<PERSON><PERSON>, label: '<PERSON>ere<PERSON>' },
    { username: 'caixa', password: 'password', role: 'cashier' as UserRole, label: 'Caixa' },
    { username: 'cozinha', password: 'password', role: 'kitchen' as UserRole, label: 'Cozinha' },
  ];

  // Redirecionar se já estiver autenticado
  useEffect(() => {
    if (isAuthenticated) {
      window.location.href = '/dashboard';
    }
  }, [isAuthenticated]);

  // Limpar erro quando componente montar
  useEffect(() => {
    clearError();
  }, [clearError]);

  const handleInputChange = (field: keyof LoginRequest) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setFormData(prev => ({
      ...prev,
      [field]: event.target.value,
    }));

    // Limpar erro quando usuário começar a digitar
    if (error) {
      clearError();
    }
  };

  const handleRoleSelect = (role: UserRole) => {
    const user = predefinedUsers.find(u => u.role === role);
    if (user) {
      setFormData({
        username: user.username,
        password: user.password,
      });
      setSelectedRole(role);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!formData.username || !formData.password) {
      return;
    }

    try {
      await login(formData);
    } catch (error) {
      // Erro já é tratado no store
      console.error('Login failed:', error);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(prev => !prev);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: 2,
      }}
    >
      <Card
        sx={{
          maxWidth: 450,
          width: '100%',
          borderRadius: 4,
          boxShadow: '0 20px 40px rgba(0,0,0,0.15)',
          backdropFilter: 'blur(10px)',
          background: 'rgba(255,255,255,0.95)',
        }}
      >
        <CardContent sx={{ padding: 4 }}>
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Store sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
              Adib PDV
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Sistema de Ponto de Venda
            </Typography>
          </Box>

          {/* Seleção Rápida de Perfil */}
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'grey.50' }}>
            <Typography variant="subtitle2" gutterBottom>
              Acesso Rápido (Demo):
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {predefinedUsers.map((user) => (
                <Button
                  key={user.role}
                  variant={selectedRole === user.role ? 'contained' : 'outlined'}
                  size="small"
                  onClick={() => handleRoleSelect(user.role)}
                  startIcon={
                    user.role === 'manager' ? <Person /> :
                    user.role === 'cashier' ? <Computer /> : <Store />
                  }
                >
                  {user.label}
                </Button>
              ))}
            </Box>
          </Paper>

          <Divider sx={{ mb: 3 }}>ou</Divider>

          {/* Formulário de Login */}
          <form onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="Usuário"
              value={formData.username}
              onChange={handleInputChange('username')}
              margin="normal"
              required
              autoComplete="username"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Person color="action" />
                  </InputAdornment>
                ),
              }}
              disabled={isLoading}
            />

            <TextField
              fullWidth
              label="Senha"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              margin="normal"
              required
              autoComplete="current-password"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Lock color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={togglePasswordVisibility}
                      edge="end"
                      disabled={isLoading}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              disabled={isLoading}
            />

            {/* Erro */}
            {error && (
              <Alert severity="error" sx={{ mt: 2 }}>
                {error}
              </Alert>
            )}

            {/* Botão de Login */}
            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading || !formData.username || !formData.password}
              sx={{
                mt: 3,
                mb: 2,
                height: 48,
                borderRadius: 2,
              }}
            >
              {isLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Entrar'
              )}
            </Button>
          </form>

          {/* Informações de Demo */}
          <Paper sx={{ p: 2, mt: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
            <Typography variant="caption" display="block">
              <strong>Credenciais de Demo:</strong>
            </Typography>
            <Typography variant="caption" display="block">
              • Gerente: admin / password
            </Typography>
            <Typography variant="caption" display="block">
              • Caixa: caixa / password
            </Typography>
            <Typography variant="caption" display="block">
              • Cozinha: cozinha / password
            </Typography>
          </Paper>

          {/* Footer */}
          <Box sx={{ textAlign: 'center', mt: 3 }}>
            <Typography variant="caption" color="text.secondary">
              Adib PDV v1.0.0 - Sistema Offline-First
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Login;

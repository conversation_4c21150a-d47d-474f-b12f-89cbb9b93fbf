import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Divider,
  Alert,
  Tab,
  Tabs,
  Paper,
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Store,
  Receipt,
  CreditCard,
  Sync,
  Security,
  Notifications,
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const Settings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [settings, setSettings] = useState({
    storeName: 'Adib PDV',
    storeAddress: '',
    storeCnpj: '',
    fiscalEnabled: true,
    tefEnabled: false,
    syncEnabled: true,
    notificationsEnabled: true,
    autoBackup: true,
    printerEnabled: true,
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSettingChange = (setting: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings(prev => ({
      ...prev,
      [setting]: event.target.type === 'checkbox' ? event.target.checked : event.target.value
    }));
  };

  const handleSave = () => {
    // Implementar salvamento das configurações
    console.log('Salvando configurações:', settings);
  };

  return (
    <Box sx={{ flexGrow: 1 }}>
      <Paper
        sx={{
          p: 3,
          mb: 3,
          background: 'linear-gradient(135deg, #0070F3 0%, #37ECC8 100%)',
          color: 'white',
          borderRadius: 3,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <SettingsIcon sx={{ fontSize: 40 }} />
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 700 }}>
              Configurações
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.9 }}>
              Gerencie as configurações do sistema PDV
            </Typography>
          </Box>
        </Box>
      </Paper>

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="configurações">
            <Tab icon={<Store />} label="Loja" />
            <Tab icon={<Receipt />} label="Fiscal" />
            <Tab icon={<CreditCard />} label="TEF" />
            <Tab icon={<Sync />} label="Sincronização" />
            <Tab icon={<Security />} label="Segurança" />
            <Tab icon={<Notifications />} label="Notificações" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            Informações da Loja
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Nome da Loja"
                value={settings.storeName}
                onChange={handleSettingChange('storeName')}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="CNPJ"
                value={settings.storeCnpj}
                onChange={handleSettingChange('storeCnpj')}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Endereço"
                multiline
                rows={3}
                value={settings.storeAddress}
                onChange={handleSettingChange('storeAddress')}
              />
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Configurações Fiscais
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={settings.fiscalEnabled}
                onChange={handleSettingChange('fiscalEnabled')}
              />
            }
            label="Emissão Fiscal Habilitada"
          />
          <Alert severity="info" sx={{ mt: 2 }}>
            Configurações fiscais requerem certificado digital válido.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Configurações TEF
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={settings.tefEnabled}
                onChange={handleSettingChange('tefEnabled')}
              />
            }
            label="TEF Habilitado"
          />
          <Alert severity="warning" sx={{ mt: 2 }}>
            TEF requer configuração específica da operadora.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>
            Sincronização
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={settings.syncEnabled}
                onChange={handleSettingChange('syncEnabled')}
              />
            }
            label="Sincronização Automática"
          />
          <FormControlLabel
            control={
              <Switch
                checked={settings.autoBackup}
                onChange={handleSettingChange('autoBackup')}
              />
            }
            label="Backup Automático"
          />
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          <Typography variant="h6" gutterBottom>
            Segurança
          </Typography>
          <Alert severity="info">
            Configurações de segurança e controle de acesso.
          </Alert>
        </TabPanel>

        <TabPanel value={tabValue} index={5}>
          <Typography variant="h6" gutterBottom>
            Notificações
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={settings.notificationsEnabled}
                onChange={handleSettingChange('notificationsEnabled')}
              />
            }
            label="Notificações Habilitadas"
          />
          <FormControlLabel
            control={
              <Switch
                checked={settings.printerEnabled}
                onChange={handleSettingChange('printerEnabled')}
              />
            }
            label="Impressora Habilitada"
          />
        </TabPanel>

        <Divider />
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
            <Button variant="outlined">
              Cancelar
            </Button>
            <Button variant="contained" onClick={handleSave}>
              Salvar Configurações
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default Settings;

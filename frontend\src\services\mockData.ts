// Mock data service para desenvolvimento e testes
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stock: number;
  image?: string;
  barcode?: string;
  active: boolean;
}

export interface Category {
  id: string;
  name: string;
  description: string;
  color: string;
}

export interface Sale {
  id: string;
  date: string;
  total: number;
  items: SaleItem[];
  customer?: Customer;
  paymentMethod: string;
  status: 'completed' | 'pending' | 'cancelled';
}

export interface SaleItem {
  productId: string;
  productName: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface Customer {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface CashMovement {
  id: string;
  type: 'opening' | 'closing' | 'withdrawal' | 'deposit' | 'sale';
  amount: number;
  description: string;
  date: string;
  userId: string;
}

export interface KitchenOrder {
  id: string;
  orderNumber: string;
  table?: string;
  items: OrderItem[];
  status: 'new' | 'preparing' | 'ready' | 'delivered';
  createdAt: string;
  estimatedTime: number;
  priority: 'low' | 'normal' | 'high';
}

export interface OrderItem {
  productName: string;
  quantity: number;
  notes?: string;
}

// Mock data
export const mockCategories: Category[] = [
  { id: '1', name: '<PERSON>b<PERSON>rguer<PERSON>', description: 'Hambúrgueres artesanais', color: '#0070F3' },
  { id: '2', name: 'Pizzas', description: 'Pizzas tradicionais', color: '#37ECC8' },
  { id: '3', name: 'Bebidas', description: 'Bebidas geladas', color: '#FFBA08' },
  { id: '4', name: 'Sobremesas', description: 'Doces e sobremesas', color: '#0A0F24' },
];

export const mockProducts: Product[] = [
  {
    id: '1',
    name: 'Hambúrguer Clássico',
    description: 'Hambúrguer com carne, queijo, alface e tomate',
    price: 25.90,
    category: 'Hambúrgueres',
    stock: 50,
    barcode: '7891234567890',
    active: true,
  },
  {
    id: '2',
    name: 'Pizza Margherita',
    description: 'Pizza com molho de tomate, mussarela e manjericão',
    price: 35.90,
    category: 'Pizzas',
    stock: 30,
    barcode: '7891234567891',
    active: true,
  },
  {
    id: '3',
    name: 'Refrigerante Cola',
    description: 'Refrigerante de cola 350ml',
    price: 5.90,
    category: 'Bebidas',
    stock: 100,
    barcode: '7891234567892',
    active: true,
  },
  {
    id: '4',
    name: 'Pudim de Leite',
    description: 'Pudim de leite condensado com calda de caramelo',
    price: 12.90,
    category: 'Sobremesas',
    stock: 20,
    barcode: '7891234567893',
    active: true,
  },
  {
    id: '5',
    name: 'Hambúrguer Bacon',
    description: 'Hambúrguer com carne, bacon, queijo e molho especial',
    price: 32.90,
    category: 'Hambúrgueres',
    stock: 40,
    barcode: '7891234567894',
    active: true,
  },
];

export const mockSales: Sale[] = [
  {
    id: '1',
    date: new Date().toISOString(),
    total: 58.80,
    items: [
      { productId: '1', productName: 'Hambúrguer Clássico', quantity: 2, unitPrice: 25.90, total: 51.80 },
      { productId: '3', productName: 'Refrigerante Cola', quantity: 1, unitPrice: 5.90, total: 5.90 },
    ],
    paymentMethod: 'Cartão de Crédito',
    status: 'completed',
  },
  {
    id: '2',
    date: new Date(Date.now() - 3600000).toISOString(),
    total: 35.90,
    items: [
      { productId: '2', productName: 'Pizza Margherita', quantity: 1, unitPrice: 35.90, total: 35.90 },
    ],
    paymentMethod: 'Dinheiro',
    status: 'completed',
  },
];

export const mockKitchenOrders: KitchenOrder[] = [
  {
    id: '1',
    orderNumber: '001',
    table: 'Mesa 5',
    items: [
      { productName: 'Hambúrguer Clássico', quantity: 2 },
      { productName: 'Batata Frita', quantity: 1 },
    ],
    status: 'preparing',
    createdAt: new Date(Date.now() - 600000).toISOString(),
    estimatedTime: 15,
    priority: 'normal',
  },
  {
    id: '2',
    orderNumber: '002',
    table: 'Balcão',
    items: [
      { productName: 'Pizza Margherita', quantity: 1 },
    ],
    status: 'new',
    createdAt: new Date(Date.now() - 300000).toISOString(),
    estimatedTime: 20,
    priority: 'high',
  },
  {
    id: '3',
    orderNumber: '003',
    table: 'Delivery',
    items: [
      { productName: 'Hambúrguer Bacon', quantity: 3 },
      { productName: 'Refrigerante Cola', quantity: 3 },
    ],
    status: 'ready',
    createdAt: new Date(Date.now() - 1200000).toISOString(),
    estimatedTime: 25,
    priority: 'normal',
  },
];

export const mockCashMovements: CashMovement[] = [
  {
    id: '1',
    type: 'opening',
    amount: 200.00,
    description: 'Abertura de caixa',
    date: new Date().toISOString(),
    userId: 'user1',
  },
  {
    id: '2',
    type: 'sale',
    amount: 58.80,
    description: 'Venda #001',
    date: new Date(Date.now() - 3600000).toISOString(),
    userId: 'user1',
  },
  {
    id: '3',
    type: 'sale',
    amount: 35.90,
    description: 'Venda #002',
    date: new Date(Date.now() - 1800000).toISOString(),
    userId: 'user1',
  },
];

// Mock API functions
export const mockApi = {
  // Products
  getProducts: async (): Promise<Product[]> => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simular delay
    return mockProducts;
  },

  getProductById: async (id: string): Promise<Product | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockProducts.find(p => p.id === id) || null;
  },

  createProduct: async (product: Omit<Product, 'id'>): Promise<Product> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const newProduct = { ...product, id: Date.now().toString() };
    mockProducts.push(newProduct);
    return newProduct;
  },

  updateProduct: async (id: string, product: Partial<Product>): Promise<Product> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const index = mockProducts.findIndex(p => p.id === id);
    if (index === -1) throw new Error('Product not found');
    mockProducts[index] = { ...mockProducts[index], ...product };
    return mockProducts[index];
  },

  deleteProduct: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const index = mockProducts.findIndex(p => p.id === id);
    if (index === -1) throw new Error('Product not found');
    mockProducts.splice(index, 1);
  },

  // Categories
  getCategories: async (): Promise<Category[]> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockCategories;
  },

  // Sales
  getSales: async (): Promise<Sale[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return mockSales;
  },

  createSale: async (sale: Omit<Sale, 'id'>): Promise<Sale> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const newSale = { ...sale, id: Date.now().toString() };
    mockSales.push(newSale);
    return newSale;
  },

  // Kitchen Orders
  getKitchenOrders: async (): Promise<KitchenOrder[]> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockKitchenOrders;
  },

  updateOrderStatus: async (id: string, status: KitchenOrder['status']): Promise<KitchenOrder> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const index = mockKitchenOrders.findIndex(o => o.id === id);
    if (index === -1) throw new Error('Order not found');
    mockKitchenOrders[index].status = status;
    return mockKitchenOrders[index];
  },

  // Cash Movements
  getCashMovements: async (): Promise<CashMovement[]> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockCashMovements;
  },

  addCashMovement: async (movement: Omit<CashMovement, 'id'>): Promise<CashMovement> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    const newMovement = { ...movement, id: Date.now().toString() };
    mockCashMovements.push(newMovement);
    return newMovement;
  },

  // Dashboard Stats
  getDashboardStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const today = new Date().toDateString();
    const todaySales = mockSales.filter(sale => 
      new Date(sale.date).toDateString() === today
    );
    
    const totalSales = todaySales.reduce((sum, sale) => sum + sale.total, 0);
    const totalOrders = todaySales.length;
    const averageTicket = totalOrders > 0 ? totalSales / totalOrders : 0;
    
    return {
      totalSales,
      totalOrders,
      averageTicket,
      activeOrders: mockKitchenOrders.filter(o => o.status !== 'delivered').length,
      cashBalance: mockCashMovements.reduce((sum, mov) => {
        return mov.type === 'opening' || mov.type === 'deposit' || mov.type === 'sale' 
          ? sum + mov.amount 
          : sum - mov.amount;
      }, 0),
    };
  },
};

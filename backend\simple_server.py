#!/usr/bin/env python3
"""
Servidor backend simples para o sistema PDV Adib
Usando Flask + SQLite para desenvolvimento rápido
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import sqlite3
import json
import os
from datetime import datetime
import time

app = Flask(__name__)
CORS(app, origins=['http://localhost:3000'])

# Caminho para o banco de dados
DB_PATH = os.path.join(os.path.dirname(__file__), '..', '..', 'database', 'adib.db')

def get_db_connection():
    """Conectar ao banco de dados SQLite"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Para retornar dicionários
        return conn
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return None

def response_format(success=True, data=None, error=None):
    """Formato padrão de resposta da API"""
    return {
        'success': success,
        'data': data,
        'error': error,
        'timestamp': datetime.now().isoformat()
    }

@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check da API"""
    return jsonify(response_format(data={
        'status': 'healthy',
        'version': '1.0.0',
        'environment': 'development'
    }))

@app.route('/api', methods=['GET'])
def api_root():
    """Rota raiz da API"""
    return jsonify(response_format(data={
        'message': 'Adib PDV Backend API',
        'version': '1.0.0',
        'endpoints': {
            'auth': '/api/auth',
            'health': '/api/health',
            'products': '/api/products'
        }
    }))

@app.route('/api/auth/login', methods=['POST'])
def login():
    """Endpoint de login"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        # Usuários mock
        mock_users = [
            {'username': 'admin', 'password': 'password', 'role': 'manager', 'full_name': 'Administrador'},
            {'username': 'caixa', 'password': 'password', 'role': 'cashier', 'full_name': 'Operador de Caixa'},
            {'username': 'cozinha', 'password': 'password', 'role': 'kitchen', 'full_name': 'Cozinheiro'},
        ]
        
        user = next((u for u in mock_users if u['username'] == username and u['password'] == password), None)
        
        if not user:
            return jsonify(response_format(success=False, error='Credenciais inválidas')), 401
        
        # Simular delay
        time.sleep(0.5)
        
        mock_token = f"mock_token_{int(time.time())}"
        mock_user = {
            'id': 1,
            'username': user['username'],
            'full_name': user['full_name'],
            'email': f"{user['username']}@adib.com",
            'role': user['role'],
            'is_active': True,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
        }
        
        return jsonify(response_format(data={
            'user': mock_user,
            'token': mock_token,
            'expires_in': '24h'
        }))
        
    except Exception as e:
        return jsonify(response_format(success=False, error=str(e))), 500

@app.route('/api/auth/logout', methods=['POST'])
def logout():
    """Endpoint de logout"""
    return jsonify(response_format(data={'message': 'Logout realizado com sucesso'}))

@app.route('/api/products', methods=['GET'])
def get_products():
    """Buscar produtos"""
    try:
        conn = get_db_connection()
        if not conn:
            # Fallback para dados mock
            mock_products = [
                {
                    'id': 1,
                    'name': 'Hambúrguer Clássico',
                    'description': 'Hambúrguer com carne, queijo, alface e tomate',
                    'price': 25.90,
                    'category': 'Hambúrgueres',
                    'stock': 50,
                    'barcode': '7891234567890',
                    'active': True,
                },
                {
                    'id': 2,
                    'name': 'Pizza Margherita',
                    'description': 'Pizza com molho de tomate, mussarela e manjericão',
                    'price': 35.90,
                    'category': 'Pizzas',
                    'stock': 30,
                    'barcode': '7891234567891',
                    'active': True,
                }
            ]
            return jsonify(response_format(data=mock_products))
        
        cursor = conn.execute('SELECT * FROM products WHERE active = 1')
        products = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify(response_format(data=products))
        
    except Exception as e:
        print(f"❌ Erro ao buscar produtos: {e}")
        return jsonify(response_format(success=False, error=str(e))), 500

@app.route('/api/products/categories', methods=['GET'])
def get_categories():
    """Buscar categorias"""
    try:
        conn = get_db_connection()
        if not conn:
            # Fallback para dados mock
            mock_categories = [
                {'id': 1, 'name': 'Hambúrgueres', 'description': 'Hambúrgueres artesanais', 'color': '#0070F3'},
                {'id': 2, 'name': 'Pizzas', 'description': 'Pizzas tradicionais', 'color': '#37ECC8'},
                {'id': 3, 'name': 'Bebidas', 'description': 'Bebidas geladas', 'color': '#FFBA08'},
                {'id': 4, 'name': 'Sobremesas', 'description': 'Doces e sobremesas', 'color': '#0A0F24'},
            ]
            return jsonify(response_format(data=mock_categories))
        
        cursor = conn.execute('SELECT * FROM categories WHERE active = 1')
        categories = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return jsonify(response_format(data=categories))
        
    except Exception as e:
        print(f"❌ Erro ao buscar categorias: {e}")
        return jsonify(response_format(success=False, error=str(e))), 500

@app.errorhandler(404)
def not_found(error):
    """Handler para rotas não encontradas"""
    return jsonify(response_format(success=False, error='Endpoint not found')), 404

@app.errorhandler(500)
def internal_error(error):
    """Handler para erros internos"""
    return jsonify(response_format(success=False, error='Internal server error')), 500

if __name__ == '__main__':
    print("🚀 Starting Adib PDV Backend Server...")
    print(f"📚 API Documentation: http://localhost:3001/api")
    print(f"🏥 Health Check: http://localhost:3001/api/health")
    print(f"🔐 Auth Endpoints: http://localhost:3001/api/auth")
    print(f"🗄️  Database: {DB_PATH}")
    
    app.run(
        host='localhost',
        port=3001,
        debug=True,
        use_reloader=False  # Evitar problemas com reloader
    )

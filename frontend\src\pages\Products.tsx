import {
    Add,
    Category,
    CheckCircle,
    Delete,
    Edit,
    Inventory,
    QrCode,
    Search,
    Warning
} from '@mui/icons-material';
import {
    <PERSON>ert,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    FormControl,
    Grid,
    IconButton,
    InputAdornment,
    InputLabel,
    MenuItem,
    Select,
    TextField,
    Tooltip,
    Typography,
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { apiService as api } from '../services/api';
import { useAuthStore } from '../services/auth';
import { Category as CategoryType, Product, ProductFilters } from '../types';

const Products: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ProductFilters>({
    search: '',
    category_id: undefined,
    is_active: true,
    low_stock: false,
  });

  const { hasPermission } = useAuthStore();

  // Carregar dados da API
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Carregar categorias e produtos da API
        const [categoriesData, productsData] = await Promise.all([
          api.getCategories(),
          api.getProducts()
        ]);

        setCategories(categoriesData);
        setProducts(productsData);
        setError(null);
      } catch (err) {
        setError('Erro ao carregar produtos');
        console.error('Error loading products:', err);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Função para recarregar dados
  const reloadData = async () => {
    try {
      setLoading(true);
      const [categoriesData, productsData] = await Promise.all([
        api.getCategories(),
        api.getProducts()
      ]);
      setCategories(categoriesData);
      setProducts(productsData);
      setError(null);
    } catch (err) {
      setError('Erro ao recarregar produtos');
      console.error('Error reloading products:', err);
    } finally {
      setLoading(false);
    }
  };

  // Função para excluir produto
  const handleDeleteProduct = async (productId: number) => {
    if (!window.confirm('Tem certeza que deseja excluir este produto?')) {
      return;
    }

    try {
      await api.deleteProduct(productId);
      await reloadData(); // Recarregar lista
    } catch (err) {
      setError('Erro ao excluir produto');
      console.error('Error deleting product:', err);
    }
  };

  const getStockStatus = (product: Product) => {
    if (!product.stock_control) return 'ok';
    if (product.current_stock <= 0) return 'out';
    if (product.current_stock <= product.min_stock) return 'low';
    return 'ok';
  };

  const getStockStatusColor = (status: string) => {
    switch (status) {
      case 'out': return 'error';
      case 'low': return 'warning';
      default: return 'success';
    }
  };

  const getStockStatusText = (status: string) => {
    switch (status) {
      case 'out': return 'Sem estoque';
      case 'low': return 'Estoque baixo';
      default: return 'Estoque OK';
    }
  };

  const filteredProducts = products.filter(product => {
    if (filters.search && !product.name.toLowerCase().includes(filters.search.toLowerCase()) &&
        !product.code?.toLowerCase().includes(filters.search.toLowerCase()) &&
        !product.barcode?.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }

    if (filters.category_id && product.category_id !== filters.category_id) {
      return false;
    }

    if (filters.is_active !== undefined && product.is_active !== filters.is_active) {
      return false;
    }

    if (filters.low_stock && getStockStatus(product) !== 'low') {
      return false;
    }

    return true;
  });

  const lowStockCount = products.filter(p => getStockStatus(p) === 'low').length;
  const outOfStockCount = products.filter(p => getStockStatus(p) === 'out').length;

  if (loading) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography>Carregando produtos...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Produtos
        </Typography>
        {hasPermission('products', 'create') && (
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => console.log('Criar produto')}
          >
            Novo Produto
          </Button>
        )}
      </Box>

      {/* Alertas de estoque */}
      {(lowStockCount > 0 || outOfStockCount > 0) && (
        <Box sx={{ mb: 3 }}>
          {outOfStockCount > 0 && (
            <Alert severity="error" sx={{ mb: 1 }}>
              {outOfStockCount} produto(s) sem estoque
            </Alert>
          )}
          {lowStockCount > 0 && (
            <Alert severity="warning" sx={{ mb: 1 }}>
              {lowStockCount} produto(s) com estoque baixo
            </Alert>
          )}
        </Box>
      )}

      {/* Filtros */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Buscar produtos"
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Categoria</InputLabel>
                <Select
                  value={filters.category_id || ''}
                  onChange={(e) => setFilters(prev => ({ ...prev, category_id: e.target.value ? Number(e.target.value) : undefined }))}
                  label="Categoria"
                >
                  <MenuItem value="">Todas</MenuItem>
                  {categories.map(category => (
                    <MenuItem key={category.id} value={category.id}>
                      {category.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant={filters.low_stock ? 'contained' : 'outlined'}
                color="warning"
                onClick={() => setFilters(prev => ({ ...prev, low_stock: !prev.low_stock }))}
                startIcon={<Warning />}
              >
                Estoque Baixo
              </Button>
            </Grid>
            <Grid item xs={12} md={3}>
              <Typography variant="body2" color="text.secondary">
                {filteredProducts.length} de {products.length} produtos
              </Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Lista de produtos */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={2}>
        {filteredProducts.map((product) => {
          const stockStatus = getStockStatus(product);

          return (
            <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  {/* Categoria */}
                  <Chip
                    label={product.category_name || 'Sem categoria'}
                    size="small"
                    sx={{
                      backgroundColor: product.category_color || '#gray',
                      color: 'white',
                      mb: 1,
                    }}
                  />

                  {/* Nome e código */}
                  <Typography variant="h6" component="h2" gutterBottom>
                    {product.name}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {product.code}
                    {product.barcode && (
                      <Tooltip title={`Código de barras: ${product.barcode}`}>
                        <QrCode sx={{ ml: 1, fontSize: 16 }} />
                      </Tooltip>
                    )}
                  </Typography>

                  {/* Preço */}
                  <Typography variant="h5" color="primary" gutterBottom>
                    R$ {product.price.toFixed(2)}
                  </Typography>

                  {/* Estoque */}
                  {product.stock_control && (
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Inventory sx={{ mr: 1, fontSize: 16 }} />
                      <Typography variant="body2">
                        {product.current_stock} {product.unit}
                      </Typography>
                      <Chip
                        label={getStockStatusText(stockStatus)}
                        size="small"
                        color={getStockStatusColor(stockStatus) as any}
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  )}

                  {/* Status */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    {product.is_active ? (
                      <CheckCircle color="success" sx={{ mr: 1, fontSize: 16 }} />
                    ) : (
                      <Warning color="warning" sx={{ mr: 1, fontSize: 16 }} />
                    )}
                    <Typography variant="body2">
                      {product.is_active ? 'Ativo' : 'Inativo'}
                    </Typography>
                  </Box>

                  {/* Ações */}
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    {hasPermission('products', 'update') && (
                      <Tooltip title="Editar">
                        <IconButton
                          size="small"
                          onClick={() => console.log('Editar produto', product.id)}
                        >
                          <Edit />
                        </IconButton>
                      </Tooltip>
                    )}

                    {hasPermission('products', 'delete') && (
                      <Tooltip title="Excluir">
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteProduct(product.id)}
                        >
                          <Delete />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      {filteredProducts.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Category sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary">
            Nenhum produto encontrado
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Ajuste os filtros ou cadastre novos produtos
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default Products;

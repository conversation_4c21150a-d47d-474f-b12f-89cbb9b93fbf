// Páginas adicionais para o servidor

// Página do PDV (Ponto de Venda)
function getPOSPage(mockData) {
  return `
    <div class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <span class="material-icons icon-lg">point_of_sale</span>
            Adib PDV - Ponto de Venda
          </div>
          <div class="nav">
            <a href="/dashboard" class="btn btn-outline">Dashboard</a>
            <a href="/pos" class="btn btn-outline">PDV</a>
            <a href="/kitchen" class="btn btn-outline">Cozinha</a>
            <a href="/products" class="btn btn-outline">Produtos</a>
            <button onclick="logout()" class="btn btn-outline">Sair</button>
          </div>
        </div>
      </div>
    </div>

    <div class="main">
      <div class="container">
        <div style="display: grid; grid-template-columns: 1fr 400px; gap: 24px; height: calc(100vh - 140px);">
          <div class="card" style="overflow: hidden; display: flex; flex-direction: column;">
            <div class="card-header">
              <h3>Produtos</h3>
            </div>
            <div class="card-content" style="flex: 1; overflow-y: auto;">
              <div class="grid grid-3" id="products-grid">
                ${mockData.products.map(product => `
                  <div class="card" onclick="addToCart(${product.id})" style="cursor: pointer; transition: all 0.2s ease;">
                    <div class="card-content text-center">
                      <div style="width: 48px; height: 48px; background: var(--primary); border-radius: 12px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center;">
                        <span class="material-icons" style="color: white;">fastfood</span>
                      </div>
                      <h4 style="margin: 0 0 8px 0; font-size: 16px;">${product.name}</h4>
                      <p style="margin: 0; color: var(--primary); font-weight: 600; font-size: 18px;">R$ ${product.price.toFixed(2)}</p>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          </div>

          <div class="card" style="display: flex; flex-direction: column;">
            <div class="card-header">
              <h3>Carrinho</h3>
            </div>
            <div class="card-content" style="flex: 1; overflow-y: auto;">
              <div id="cart-items" style="display: flex; flex-direction: column; gap: 12px;">
                <!-- Itens do carrinho serão adicionados aqui -->
              </div>
            </div>
            <div class="card-footer">
              <div style="border-top: 1px solid #E5E7EB; padding-top: 16px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                  <span style="font-size: 18px; font-weight: 600;">Total:</span>
                  <span id="cart-total" style="font-size: 24px; font-weight: 700; color: var(--primary);">R$ 0,00</span>
                </div>
                <button onclick="finalizeSale()" class="btn btn-primary" style="width: 100%; margin-bottom: 8px;">
                  <span class="material-icons icon">shopping_cart_checkout</span>
                  Finalizar Venda
                </button>
                <button onclick="clearCart()" class="btn btn-outline" style="width: 100%; color: var(--dark); border-color: #E5E7EB;">
                  <span class="material-icons icon">clear</span>
                  Limpar Carrinho
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      let cart = [];
      const products = ${JSON.stringify(mockData.products)};

      function addToCart(productId) {
        const product = products.find(p => p.id === productId);
        const existingItem = cart.find(item => item.id === productId);

        if (existingItem) {
          existingItem.quantity++;
        } else {
          cart.push({ ...product, quantity: 1 });
        }

        renderCart();
      }

      function removeFromCart(productId) {
        const itemIndex = cart.findIndex(item => item.id === productId);
        if (itemIndex > -1) {
          if (cart[itemIndex].quantity > 1) {
            cart[itemIndex].quantity--;
          } else {
            cart.splice(itemIndex, 1);
          }
          renderCart();
        }
      }

      function renderCart() {
        const cartItems = document.getElementById('cart-items');
        const cartTotal = document.getElementById('cart-total');

        if (cart.length === 0) {
          cartItems.innerHTML = '<div class="text-center text-muted" style="padding: 40px 0;">Carrinho vazio</div>';
        } else {
          cartItems.innerHTML = cart.map(item => \`
            <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #F9FAFB; border-radius: 8px;">
              <div style="flex: 1;">
                <div style="font-weight: 500; margin-bottom: 4px;">\${item.name}</div>
                <div class="text-muted" style="font-size: 14px;">R$ \${item.price.toFixed(2)} cada</div>
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <button onclick="removeFromCart(\${item.id})" class="btn" style="width: 32px; height: 32px; padding: 0; background: #EF4444; color: white; border-radius: 6px;">
                  <span class="material-icons" style="font-size: 16px;">remove</span>
                </button>
                <span style="min-width: 24px; text-align: center; font-weight: 600;">\${item.quantity}</span>
                <button onclick="addToCart(\${item.id})" class="btn" style="width: 32px; height: 32px; padding: 0; background: var(--primary); color: white; border-radius: 6px;">
                  <span class="material-icons" style="font-size: 16px;">add</span>
                </button>
              </div>
            </div>
          \`).join('');
        }

        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        cartTotal.textContent = \`R$ \${total.toFixed(2)}\`;
      }

      function clearCart() {
        cart = [];
        renderCart();
      }

      function finalizeSale() {
        if (cart.length === 0) {
          alert('Carrinho vazio!');
          return;
        }

        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        alert(\`Venda finalizada com sucesso!\\nTotal: R$ \${total.toFixed(2)}\`);
        clearCart();
      }

      function logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/';
      }

      // Verificar se está logado
      if (!localStorage.getItem('token')) {
        window.location.href = '/';
      }

      // Renderizar carrinho inicial
      renderCart();
    </script>
  `;
}

// Página da Cozinha
function getKitchenPage(mockData) {
  return `
    <div class="header">
      <div class="container">
        <div class="header-content">
          <div class="logo">
            <span class="material-icons icon-lg">restaurant</span>
            Adib PDV - Sistema de Cozinha
          </div>
          <div class="nav">
            <a href="/dashboard" class="btn btn-outline">Dashboard</a>
            <a href="/pos" class="btn btn-outline">PDV</a>
            <a href="/kitchen" class="btn btn-outline">Cozinha</a>
            <a href="/products" class="btn btn-outline">Produtos</a>
            <button onclick="logout()" class="btn btn-outline">Sair</button>
          </div>
        </div>
      </div>
    </div>

    <div class="main">
      <div class="container">
        <h1 class="mb-6">Sistema de Cozinha</h1>

        <div class="grid grid-3" id="orders-grid">
          ${mockData.orders.map(order => {
            const timeAgo = Math.floor((Date.now() - new Date(order.created_at).getTime()) / 60000);
            const statusText = {
              'pending': 'Pendente',
              'preparing': 'Preparando',
              'ready': 'Pronto'
            };

            return `
              <div class="card" style="border-left: 4px solid ${order.status === 'pending' ? '#FFBA08' : order.status === 'preparing' ? '#0070F3' : '#37ECC8'};">
                <div class="card-content">
                  <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                    <h3 style="margin: 0;">${order.number}</h3>
                    <span class="text-muted" style="font-size: 14px;">${timeAgo} min atrás</span>
                  </div>

                  <span class="status-badge status-${order.status}" style="margin-bottom: 16px; display: inline-block;">
                    ${statusText[order.status]}
                  </span>

                  <div style="margin-bottom: 16px;">
                    ${order.items.map(item => `
                      <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                        <span>${item.quantity}x ${item.name}</span>
                      </div>
                    `).join('')}
                  </div>

                  <div style="display: flex; gap: 8px;">
                    ${order.status === 'pending' ? `
                      <button onclick="startPreparing(${order.id})" class="btn btn-primary" style="flex: 1;">
                        <span class="material-icons icon">play_arrow</span>
                        Iniciar Preparo
                      </button>
                    ` : ''}
                    ${order.status === 'preparing' ? `
                      <button onclick="markReady(${order.id})" class="btn btn-secondary" style="flex: 1;">
                        <span class="material-icons icon">check</span>
                        Marcar como Pronto
                      </button>
                    ` : ''}
                    ${order.status === 'ready' ? `
                      <button onclick="completeOrder(${order.id})" class="btn btn-accent" style="flex: 1;">
                        <span class="material-icons icon">delivery_dining</span>
                        Entregar Pedido
                      </button>
                    ` : ''}
                  </div>
                </div>
              </div>
            `;
          }).join('')}
        </div>

        ${mockData.orders.length === 0 ? `
          <div class="text-center" style="padding: 60px 0;">
            <span class="material-icons" style="font-size: 64px; color: #9CA3AF; margin-bottom: 16px;">restaurant</span>
            <h3 class="text-muted">Nenhum pedido na fila</h3>
            <p class="text-muted">Aguardando novos pedidos...</p>
          </div>
        ` : ''}
      </div>
    </div>

    <script>
      let orders = ${JSON.stringify(mockData.orders)};

      async function startPreparing(orderId) {
        try {
          // Simular API call para atualizar status
          const response = await fetch(\`/api/orders/\${orderId}/status\`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: 'preparing' })
          });

          if (response.ok || response.status === 404) {
            // Atualizar localmente mesmo se a API não existir ainda
            const order = orders.find(o => o.id === orderId);
            if (order) {
              order.status = 'preparing';
              renderOrders();
            }
          }
        } catch (error) {
          // Fallback: atualizar localmente
          const order = orders.find(o => o.id === orderId);
          if (order) {
            order.status = 'preparing';
            renderOrders();
          }
        }
      }

      async function markReady(orderId) {
        try {
          // Simular API call para atualizar status
          const response = await fetch(\`/api/orders/\${orderId}/status\`, {
            method: 'PATCH',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: 'ready' })
          });

          if (response.ok || response.status === 404) {
            // Atualizar localmente mesmo se a API não existir ainda
            const order = orders.find(o => o.id === orderId);
            if (order) {
              order.status = 'ready';
              renderOrders();
            }
          }
        } catch (error) {
          // Fallback: atualizar localmente
          const order = orders.find(o => o.id === orderId);
          if (order) {
            order.status = 'ready';
            renderOrders();
          }
        }
      }

      async function completeOrder(orderId) {
        try {
          // Simular API call para completar pedido
          const response = await fetch(\`/api/orders/\${orderId}\`, {
            method: 'DELETE'
          });

          if (response.ok || response.status === 404) {
            // Atualizar localmente mesmo se a API não existir ainda
            orders = orders.filter(o => o.id !== orderId);
            renderOrders();
          }
        } catch (error) {
          // Fallback: atualizar localmente
          orders = orders.filter(o => o.id !== orderId);
          renderOrders();
        }
      }

      function renderOrders() {
        const ordersGrid = document.getElementById('orders-grid');

        if (orders.length === 0) {
          ordersGrid.innerHTML = \`
            <div style="grid-column: 1 / -1; text-align: center; padding: 60px 0;">
              <span class="material-icons" style="font-size: 64px; color: #9CA3AF; margin-bottom: 16px;">restaurant</span>
              <h3 class="text-muted">Nenhum pedido na fila</h3>
              <p class="text-muted">Aguardando novos pedidos...</p>
            </div>
          \`;
          return;
        }

        ordersGrid.innerHTML = orders.map(order => {
          const timeAgo = Math.floor((Date.now() - new Date(order.created_at).getTime()) / 60000);
          const statusText = {
            'pending': 'Pendente',
            'preparing': 'Preparando',
            'ready': 'Pronto'
          };

          return \`
            <div class="card" style="border-left: 4px solid \${order.status === 'pending' ? '#FFBA08' : order.status === 'preparing' ? '#0070F3' : '#37ECC8'};">
              <div class="card-content">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                  <h3 style="margin: 0;">\${order.number}</h3>
                  <span class="text-muted" style="font-size: 14px;">\${timeAgo} min atrás</span>
                </div>

                <span class="status-badge status-\${order.status}" style="margin-bottom: 16px; display: inline-block;">
                  \${statusText[order.status]}
                </span>

                <div style="margin-bottom: 16px;">
                  \${order.items.map(item => \`
                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                      <span>\${item.quantity}x \${item.name}</span>
                    </div>
                  \`).join('')}
                </div>

                <div style="display: flex; gap: 8px;">
                  \${order.status === 'pending' ? \`
                    <button onclick="startPreparing(\${order.id})" class="btn btn-primary" style="flex: 1;">
                      <span class="material-icons icon">play_arrow</span>
                      Iniciar Preparo
                    </button>
                  \` : ''}
                  \${order.status === 'preparing' ? \`
                    <button onclick="markReady(\${order.id})" class="btn btn-secondary" style="flex: 1;">
                      <span class="material-icons icon">check</span>
                      Marcar como Pronto
                    </button>
                  \` : ''}
                  \${order.status === 'ready' ? \`
                    <button onclick="completeOrder(\${order.id})" class="btn btn-accent" style="flex: 1;">
                      <span class="material-icons icon">delivery_dining</span>
                      Entregar Pedido
                    </button>
                  \` : ''}
                </div>
              </div>
            </div>
          \`;
        }).join('');
      }

      function logout() {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/';
      }

      // Verificar se está logado
      if (!localStorage.getItem('token')) {
        window.location.href = '/';
      }

      // Renderizar pedidos inicial
      renderOrders();

      // Atualizar a cada 30 segundos
      setInterval(() => {
        renderOrders();
      }, 30000);
    </script>
  `;
}

module.exports = { getPOSPage, getKitchenPage };

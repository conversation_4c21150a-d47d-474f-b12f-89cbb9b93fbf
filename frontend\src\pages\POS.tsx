import {
    Add,
    Clear,
    LocalShipping,
    Payment,
    Remove,
    Search,
    ShoppingCart,
    TableRestaurant
} from '@mui/icons-material';
import {
    Badge,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Divider,
    FormControl,
    Grid,
    IconButton,
    InputAdornment,
    InputLabel,
    List,
    ListItem,
    ListItemSecondaryAction,
    ListItemText,
    MenuItem,
    Select,
    TextField,
    Typography
} from '@mui/material';
import React, { useEffect, useState } from 'react';
import { apiService as api } from '../services/api';
import { useAuthStore } from '../services/auth';
import { Category, Product } from '../types';

interface CartItem {
  product: Product;
  quantity: number;
  notes?: string;
}

interface OrderData {
  type: 'counter' | 'table' | 'delivery';
  customer_id?: number;
  table_id?: number;
  notes?: string;
  delivery_address?: string;
}

const POS: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [orderData, setOrderData] = useState<OrderData>({ type: 'counter' });
  const [paymentDialog, setPaymentDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  const { user } = useAuthStore();

  // Carregar dados da API
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Carregar categorias e produtos da API
        const [categoriesData, productsData] = await Promise.all([
          api.getCategories(),
          api.getProducts()
        ]);

        setCategories(categoriesData);
        setProducts(productsData);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Filtrar produtos
  const filteredProducts = products.filter(product => {
    const matchesCategory = !selectedCategory || product.category_id === selectedCategory;
    const matchesSearch = !searchTerm ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.code?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch && product.is_active;
  });

  // Adicionar produto ao carrinho
  const addToCart = (product: Product) => {
    const existingItem = cart.find(item => item.product.id === product.id);

    if (existingItem) {
      setCart(cart.map(item =>
        item.product.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { product, quantity: 1 }]);
    }
  };

  // Remover produto do carrinho
  const removeFromCart = (productId: number) => {
    const existingItem = cart.find(item => item.product.id === productId);

    if (existingItem && existingItem.quantity > 1) {
      setCart(cart.map(item =>
        item.product.id === productId
          ? { ...item, quantity: item.quantity - 1 }
          : item
      ));
    } else {
      setCart(cart.filter(item => item.product.id !== productId));
    }
  };

  // Limpar carrinho
  const clearCart = () => {
    setCart([]);
  };

  // Calcular totais
  const subtotal = cart.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const deliveryFee = orderData.type === 'delivery' ? 5.00 : 0;
  const total = subtotal + deliveryFee;

  // Finalizar pedido
  const finishOrder = () => {
    if (cart.length === 0) {
      return;
    }
    setPaymentDialog(true);
  };

  // Processar pagamento
  const processPayment = async () => {
    try {
      setLoading(true);

      // Criar pedido na API
      const orderPayload = {
        type: orderData.type,
        customer_id: orderData.customer_id,
        table_id: orderData.table_id,
        user_id: user?.id,
        items: cart.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          notes: item.notes,
        })),
        notes: orderData.notes,
        delivery_address: orderData.delivery_address,
        delivery_fee: deliveryFee,
        discount: 0,
      };

      const orderResult = await api.createOrder(orderPayload);

      // Simular pagamento em dinheiro por enquanto
      await api.addPayment(orderResult.id, {
        method: 'cash',
        amount: total,
        received_amount: total,
      });

      console.log('✅ Pedido criado com sucesso:', orderResult);

      // Limpar carrinho e fechar dialog
      clearCart();
      setPaymentDialog(false);
      setOrderData({ type: 'counter' });
    } catch (error) {
      console.error('❌ Erro ao processar pedido:', error);
      alert('Erro ao processar pedido. Tente novamente.');
    } finally {
      setLoading(false);
    }
  };

  const cartItemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              PDV - Ponto de Venda
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Tipo</InputLabel>
                <Select
                  value={orderData.type}
                  onChange={(e) => setOrderData(prev => ({ ...prev, type: e.target.value as any }))}
                  label="Tipo"
                >
                  <MenuItem value="counter">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShoppingCart fontSize="small" />
                      Balcão
                    </Box>
                  </MenuItem>
                  <MenuItem value="table">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TableRestaurant fontSize="small" />
                      Mesa
                    </Box>
                  </MenuItem>
                  <MenuItem value="delivery">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocalShipping fontSize="small" />
                      Delivery
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box sx={{ flexGrow: 1, display: 'flex' }}>
        {/* Produtos */}
        <Box sx={{ flexGrow: 1, p: 2 }}>
          {/* Filtros */}
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              placeholder="Buscar produtos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />

            {/* Categorias */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                label="Todas"
                onClick={() => setSelectedCategory(null)}
                color={selectedCategory === null ? 'primary' : 'default'}
                variant={selectedCategory === null ? 'filled' : 'outlined'}
              />
              {categories.map(category => (
                <Chip
                  key={category.id}
                  label={category.name}
                  onClick={() => setSelectedCategory(category.id)}
                  color={selectedCategory === category.id ? 'primary' : 'default'}
                  variant={selectedCategory === category.id ? 'filled' : 'outlined'}
                  sx={{
                    backgroundColor: selectedCategory === category.id ? category.color : undefined,
                    '&:hover': {
                      backgroundColor: selectedCategory === category.id ? category.color : undefined,
                    },
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* Grid de produtos */}
          <Grid container spacing={2}>
            {filteredProducts.map(product => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'scale(1.02)',
                    },
                  }}
                  onClick={() => addToCart(product)}
                >
                  <CardContent>
                    <Chip
                      label={product.category_name}
                      size="small"
                      sx={{
                        backgroundColor: product.category_color,
                        color: 'white',
                        mb: 1,
                      }}
                    />
                    <Typography variant="h6" component="h3" gutterBottom>
                      {product.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {product.description}
                    </Typography>
                    <Typography variant="h5" color="primary">
                      R$ {product.price.toFixed(2)}
                    </Typography>
                    {product.stock_control && (
                      <Typography variant="caption" color="text.secondary">
                        Estoque: {product.current_stock} {product.unit}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Carrinho */}
        <Box sx={{ width: 400, borderLeft: 1, borderColor: 'divider', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6">
                Carrinho
                <Badge badgeContent={cartItemCount} color="primary" sx={{ ml: 1 }}>
                  <ShoppingCart />
                </Badge>
              </Typography>
              {cart.length > 0 && (
                <IconButton onClick={clearCart} color="error">
                  <Clear />
                </IconButton>
              )}
            </Box>
          </Box>

          <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
            {cart.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <ShoppingCart sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography color="text.secondary">
                  Carrinho vazio
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Adicione produtos para começar
                </Typography>
              </Box>
            ) : (
              <List>
                {cart.map(item => (
                  <ListItem key={item.product.id}>
                    <ListItemText
                      primary={item.product.name}
                      secondary={`R$ ${item.product.price.toFixed(2)} x ${item.quantity}`}
                    />
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => removeFromCart(item.product.id)}
                        >
                          <Remove />
                        </IconButton>
                        <Typography>{item.quantity}</Typography>
                        <IconButton
                          size="small"
                          onClick={() => addToCart(item.product)}
                        >
                          <Add />
                        </IconButton>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Box>

          {cart.length > 0 && (
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Subtotal:</Typography>
                  <Typography>R$ {subtotal.toFixed(2)}</Typography>
                </Box>
                {deliveryFee > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Taxa de entrega:</Typography>
                    <Typography>R$ {deliveryFee.toFixed(2)}</Typography>
                  </Box>
                )}
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6">Total:</Typography>
                  <Typography variant="h6" color="primary">
                    R$ {total.toFixed(2)}
                  </Typography>
                </Box>
              </Box>

              <Button
                fullWidth
                variant="contained"
                size="large"
                startIcon={<Payment />}
                onClick={finishOrder}
              >
                Finalizar Pedido
              </Button>
            </Box>
          )}
        </Box>
      </Box>

      {/* Dialog de Pagamento */}
      <Dialog open={paymentDialog} onClose={() => setPaymentDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Finalizar Pedido</DialogTitle>
        <DialogContent>

          <Typography variant="h6" gutterBottom>
            Resumo do Pedido
          </Typography>

          <List dense>
            {cart.map(item => (
              <ListItem key={item.product.id}>
                <ListItemText
                  primary={`${item.quantity}x ${item.product.name}`}
                  secondary={`R$ ${item.product.price.toFixed(2)} cada`}
                />
                <Typography>
                  R$ {(item.product.price * item.quantity).toFixed(2)}
                </Typography>
              </ListItem>
            ))}
          </List>

          <Divider sx={{ my: 2 }} />

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Total:</Typography>
            <Typography variant="h6" color="primary">
              R$ {total.toFixed(2)}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPaymentDialog(false)} disabled={loading}>
            Cancelar
          </Button>
          <Button
            variant="contained"
            onClick={processPayment}
            disabled={loading}
          >
            {loading ? 'Processando...' : 'Confirmar Pagamento'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default POS;

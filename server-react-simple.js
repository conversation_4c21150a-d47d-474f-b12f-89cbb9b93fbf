const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');
const { getPOSPage, getKitchenPage } = require('./server-pages.js');

const PORT = 3000;

// Dados mock para demonstração
const mockData = {
  users: [
    { id: 1, username: 'admin', password: 'password', name: '<PERSON><PERSON><PERSON><PERSON>', role: 'manager' },
    { id: 2, username: 'caixa', password: 'password', name: '<PERSON><PERSON> de Caixa', role: 'cashier' },
    { id: 3, username: 'co<PERSON><PERSON>', password: 'password', name: '<PERSON><PERSON><PERSON><PERSON>', role: 'kitchen' }
  ],
  products: [
    { id: 1, name: 'Hambúrguer Clássico', price: 25.90, category: 'Hambúrgueres', active: true },
    { id: 2, name: 'Pizza Margherita', price: 35.00, category: 'Pizzas', active: true },
    { id: 3, name: 'Refrigeran<PERSON> Lata', price: 5.50, category: 'Bebidas', active: true },
    { id: 4, name: 'Batata Frita', price: 12.00, category: 'Acompanhamentos', active: true },
    { id: 5, name: 'Sorvete Chocolate', price: 8.50, category: 'Sobremesas', active: true }
  ],
  orders: [
    {
      id: 1,
      number: 'PED001',
      status: 'pending',
      items: [
        { name: 'Hambúrguer Clássico', quantity: 2, price: 25.90 },
        { name: 'Batata Frita', quantity: 1, price: 12.00 }
      ],
      total: 63.80,
      created_at: new Date(Date.now() - 5 * 60000).toISOString(),
      customer: 'João Silva',
      payment_method: 'Cartão de Crédito',
      operator: 'admin'
    },
    {
      id: 2,
      number: 'PED002',
      status: 'preparing',
      items: [
        { name: 'Pizza Margherita', quantity: 1, price: 35.00 }
      ],
      total: 35.00,
      created_at: new Date(Date.now() - 10 * 60000).toISOString(),
      customer: 'Maria Santos',
      payment_method: 'PIX',
      operator: 'caixa'
    },
    {
      id: 3,
      number: 'PED003',
      status: 'ready',
      items: [
        { name: 'Refrigerante Lata', quantity: 2, price: 5.50 },
        { name: 'Sorvete Chocolate', quantity: 1, price: 8.50 }
      ],
      total: 19.50,
      created_at: new Date(Date.now() - 15 * 60000).toISOString(),
      customer: 'Pedro Costa',
      payment_method: 'Dinheiro',
      operator: 'admin'
    }
  ],
  categories: [
    { id: 1, name: 'Hambúrgueres', active: true },
    { id: 2, name: 'Pizzas', active: true },
    { id: 3, name: 'Bebidas', active: true },
    { id: 4, name: 'Acompanhamentos', active: true },
    { id: 5, name: 'Sobremesas', active: true }
  ],
  sales: [
    { date: '2024-12-19', total: 1250.00, orders: 15 },
    { date: '2024-12-18', total: 980.50, orders: 12 },
    { date: '2024-12-17', total: 1450.75, orders: 18 },
    { date: '2024-12-16', total: 1100.25, orders: 14 },
    { date: '2024-12-15', total: 1350.00, orders: 16 }
  ]
};

// Função para ler o corpo da requisição
function readBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const data = JSON.parse(body);
      callback(null, data);
    } catch (error) {
      callback(error);
    }
  });
}

// Função para enviar JSON
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data));
}

// Função para gerar HTML base com design Meta
function generateHTML(title, content) {
  return `
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - Adib PDV</title>

    <!-- Fontes -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

    <style>
        :root {
            --primary: #0070F3;
            --secondary: #37ECC8;
            --accent: #FFBA08;
            --dark: #0A0F24;
            --light: #F5F5F5;
            --gradient: linear-gradient(135deg, #0070F3 0%, #37ECC8 100%);
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
            --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);
            --radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: var(--light);
            color: var(--dark);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 24px;
        }

        .header {
            background: var(--gradient);
            color: white;
            padding: 16px 0;
            box-shadow: var(--shadow-md);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 24px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .nav {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            font-family: inherit;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-secondary {
            background: var(--secondary);
            color: var(--dark);
        }

        .btn-accent {
            background: var(--accent);
            color: var(--dark);
        }

        .btn-outline {
            background: transparent;
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline:hover {
            background: rgba(255,255,255,0.1);
        }

        .card {
            background: white;
            border-radius: var(--radius);
            box-shadow: var(--shadow-sm);
            border: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .card-header {
            padding: 24px 24px 0;
        }

        .card-content {
            padding: 24px;
        }

        .card-footer {
            padding: 0 24px 24px;
        }

        .grid {
            display: grid;
            gap: 24px;
        }

        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }

        .main {
            padding: 32px 0;
            min-height: calc(100vh - 80px);
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.2;
            margin-bottom: 16px;
        }

        h1 { font-size: 2.5rem; }
        h2 { font-size: 2rem; }
        h3 { font-size: 1.5rem; }
        h4 { font-size: 1.25rem; }

        .text-center { text-align: center; }
        .text-muted { color: #6B7280; }

        .mb-4 { margin-bottom: 24px; }
        .mb-6 { margin-bottom: 32px; }
        .mt-4 { margin-top: 24px; }
        .mt-6 { margin-top: 32px; }

        .icon {
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .icon-lg {
            width: 48px;
            height: 48px;
            font-size: 24px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending { background: #FEF3C7; color: #92400E; }
        .status-preparing { background: #DBEAFE; color: #1E40AF; }
        .status-ready { background: #D1FAE5; color: #065F46; }

        .input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            font-size: 16px;
            font-family: inherit;
            transition: border-color 0.2s ease;
        }

        .input:focus {
            outline: none;
            border-color: var(--primary);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark);
        }

        @media (max-width: 768px) {
            .container { padding: 0 16px; }
            .header-content { flex-direction: column; gap: 16px; }
            .nav { flex-wrap: wrap; justify-content: center; }
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    ${content}
</body>
</html>`;
}

// Criar servidor
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  // API de Login
  if (pathname === '/api/auth/login' && method === 'POST') {
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }

      const { username, password } = data;
      const user = mockData.users.find(u => u.username === username && u.password === password);

      if (user) {
        sendJSON(res, {
          success: true,
          data: {
            token: 'demo-token-' + Date.now(),
            user: {
              id: user.id,
              username: user.username,
              name: user.name,
              role: user.role
            }
          },
          message: 'Login realizado com sucesso!'
        });
      } else {
        sendJSON(res, {
          success: false,
          error: 'Credenciais inválidas'
        }, 401);
      }
    });
  }

  // Health Check
  else if (pathname === '/api/health' && method === 'GET') {
    sendJSON(res, {
      success: true,
      message: 'Sistema PDV Adib funcionando!',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      status: 'online'
    });
  }

  // Produtos
  else if (pathname === '/api/products' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.products.filter(p => p.active),
      total: mockData.products.filter(p => p.active).length
    });
  }

  // Pedidos
  else if (pathname === '/api/orders' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.orders,
      total: mockData.orders.length
    });
  }

  // Adicionar produto
  else if (pathname === '/api/products' && method === 'POST') {
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }

      const newProduct = {
        id: Math.max(...mockData.products.map(p => p.id)) + 1,
        name: data.name,
        price: parseFloat(data.price),
        category: data.category,
        active: data.active !== false
      };

      mockData.products.push(newProduct);
      sendJSON(res, {
        success: true,
        data: newProduct,
        message: 'Produto adicionado com sucesso!'
      });
    });
  }

  // Atualizar produto
  else if (pathname.startsWith('/api/products/') && method === 'PUT') {
    const productId = parseInt(pathname.split('/')[3]);
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }

      const productIndex = mockData.products.findIndex(p => p.id === productId);
      if (productIndex === -1) {
        return sendJSON(res, { success: false, error: 'Produto não encontrado' }, 404);
      }

      mockData.products[productIndex] = {
        ...mockData.products[productIndex],
        name: data.name || mockData.products[productIndex].name,
        price: data.price !== undefined ? parseFloat(data.price) : mockData.products[productIndex].price,
        category: data.category || mockData.products[productIndex].category,
        active: data.active !== undefined ? data.active : mockData.products[productIndex].active
      };

      sendJSON(res, {
        success: true,
        data: mockData.products[productIndex],
        message: 'Produto atualizado com sucesso!'
      });
    });
  }

  // Toggle status do produto
  else if (pathname.startsWith('/api/products/') && pathname.endsWith('/toggle') && method === 'PATCH') {
    const productId = parseInt(pathname.split('/')[3]);
    const productIndex = mockData.products.findIndex(p => p.id === productId);

    if (productIndex === -1) {
      return sendJSON(res, { success: false, error: 'Produto não encontrado' }, 404);
    }

    mockData.products[productIndex].active = !mockData.products[productIndex].active;

    sendJSON(res, {
      success: true,
      data: mockData.products[productIndex],
      message: `Produto ${mockData.products[productIndex].active ? 'ativado' : 'desativado'} com sucesso!`
    });
  }

  // Categorias
  else if (pathname === '/api/categories' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.categories,
      total: mockData.categories.length
    });
  }

  // Relatórios de vendas
  else if (pathname === '/api/reports/sales' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.sales,
      summary: {
        totalSales: mockData.sales.reduce((sum, sale) => sum + sale.total, 0),
        totalOrders: mockData.sales.reduce((sum, sale) => sum + sale.orders, 0),
        averageTicket: mockData.sales.reduce((sum, sale) => sum + sale.total, 0) / mockData.sales.reduce((sum, sale) => sum + sale.orders, 0)
      }
    });
  }

  // Atualizar status do pedido
  else if (pathname.startsWith('/api/orders/') && pathname.endsWith('/status') && method === 'PATCH') {
    const orderId = parseInt(pathname.split('/')[3]);
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }

      const orderIndex = mockData.orders.findIndex(o => o.id === orderId);
      if (orderIndex === -1) {
        return sendJSON(res, { success: false, error: 'Pedido não encontrado' }, 404);
      }

      mockData.orders[orderIndex].status = data.status;

      sendJSON(res, {
        success: true,
        data: mockData.orders[orderIndex],
        message: `Status do pedido atualizado para ${data.status}`
      });
    });
  }

  // Deletar pedido (completar)
  else if (pathname.startsWith('/api/orders/') && method === 'DELETE') {
    const orderId = parseInt(pathname.split('/')[3]);
    const orderIndex = mockData.orders.findIndex(o => o.id === orderId);

    if (orderIndex === -1) {
      return sendJSON(res, { success: false, error: 'Pedido não encontrado' }, 404);
    }

    const completedOrder = mockData.orders.splice(orderIndex, 1)[0];

    sendJSON(res, {
      success: true,
      data: completedOrder,
      message: 'Pedido completado com sucesso!'
    });
  }

  // Criar novo pedido (do PDV)
  else if (pathname === '/api/orders' && method === 'POST') {
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }

      const newOrder = {
        id: Math.max(...mockData.orders.map(o => o.id)) + 1,
        number: 'PED' + String(Math.max(...mockData.orders.map(o => o.id)) + 1).padStart(3, '0'),
        status: 'pending',
        items: data.items,
        total: data.total,
        created_at: new Date().toISOString(),
        customer: data.customer || 'Cliente',
        payment_method: data.payment_method || 'Dinheiro',
        operator: data.operator || 'sistema'
      };

      mockData.orders.push(newOrder);

      sendJSON(res, {
        success: true,
        data: newOrder,
        message: 'Pedido criado com sucesso!'
      });
    });
  }

  // Página de Login
  else if (pathname === '/' && method === 'GET') {
    const content = `
      <div class="header">
        <div class="container">
          <div class="header-content">
            <div class="logo">
              <span class="material-icons icon-lg">store</span>
              Adib PDV
            </div>
          </div>
        </div>
      </div>

      <div class="main">
        <div class="container">
          <div style="max-width: 400px; margin: 0 auto;">
            <div class="card">
              <div class="card-content text-center">
                <div style="width: 80px; height: 80px; background: var(--gradient); border-radius: 50%; margin: 0 auto 24px; display: flex; align-items: center; justify-content: center;">
                  <span class="material-icons" style="font-size: 40px; color: white;">store</span>
                </div>

                <h2>Bem-vindo ao Adib PDV</h2>
                <p class="text-muted mb-6">Sistema de Ponto de Venda Moderno</p>

                <form id="loginForm">
                  <div class="form-group">
                    <label class="label">Usuário</label>
                    <input type="text" class="input" id="username" required placeholder="Digite seu usuário">
                  </div>

                  <div class="form-group">
                    <label class="label">Senha</label>
                    <input type="password" class="input" id="password" required placeholder="Digite sua senha">
                  </div>

                  <div id="error" style="color: #EF4444; margin-bottom: 16px; display: none;"></div>

                  <button type="submit" class="btn btn-primary" style="width: 100%; margin-bottom: 24px;">
                    <span id="loginText">Entrar</span>
                    <span id="loginLoading" style="display: none;">Entrando...</span>
                  </button>
                </form>

                <div style="background: #F9FAFB; padding: 16px; border-radius: 8px; border: 1px solid #E5E7EB;">
                  <h4 style="margin-bottom: 12px; font-size: 14px;">Credenciais para Teste</h4>
                  <div style="display: grid; gap: 8px;">
                    <button class="btn btn-outline" onclick="fillCredentials('admin', 'password')" style="color: var(--dark); border-color: #E5E7EB;">
                      <span class="material-icons icon">admin_panel_settings</span>
                      Administrador
                    </button>
                    <button class="btn btn-outline" onclick="fillCredentials('caixa', 'password')" style="color: var(--dark); border-color: #E5E7EB;">
                      <span class="material-icons icon">point_of_sale</span>
                      Operador de Caixa
                    </button>
                    <button class="btn btn-outline" onclick="fillCredentials('cozinha', 'password')" style="color: var(--dark); border-color: #E5E7EB;">
                      <span class="material-icons icon">restaurant</span>
                      Cozinheiro
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <script>
        function fillCredentials(username, password) {
          document.getElementById('username').value = username;
          document.getElementById('password').value = password;
        }

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
          e.preventDefault();

          const username = document.getElementById('username').value;
          const password = document.getElementById('password').value;
          const errorDiv = document.getElementById('error');
          const loginText = document.getElementById('loginText');
          const loginLoading = document.getElementById('loginLoading');

          loginText.style.display = 'none';
          loginLoading.style.display = 'inline';
          errorDiv.style.display = 'none';

          try {
            const response = await fetch('/api/auth/login', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (data.success) {
              localStorage.setItem('token', data.data.token);
              localStorage.setItem('user', JSON.stringify(data.data.user));

              switch(data.data.user.role) {
                case 'manager':
                  window.location.href = '/dashboard';
                  break;
                case 'cashier':
                  window.location.href = '/pos';
                  break;
                case 'kitchen':
                  window.location.href = '/kitchen';
                  break;
                default:
                  window.location.href = '/dashboard';
              }
            } else {
              errorDiv.textContent = data.error;
              errorDiv.style.display = 'block';
            }
          } catch (error) {
            errorDiv.textContent = 'Erro de conexão: ' + error.message;
            errorDiv.style.display = 'block';
          } finally {
            loginText.style.display = 'inline';
            loginLoading.style.display = 'none';
          }
        });
      </script>
    `;

    const html = generateHTML('Login', content);
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  }

  // Dashboard
  else if (pathname === '/dashboard' && method === 'GET') {
    const content = `
      <div class="header">
        <div class="container">
          <div class="header-content">
            <div class="logo">
              <span class="material-icons icon-lg">dashboard</span>
              Adib PDV - Dashboard
            </div>
            <div class="nav">
              <a href="/dashboard" class="btn btn-outline">Dashboard</a>
              <a href="/pos" class="btn btn-outline">PDV</a>
              <a href="/kitchen" class="btn btn-outline">Cozinha</a>
              <a href="/products" class="btn btn-outline">Produtos</a>
              <button onclick="logout()" class="btn btn-outline">Sair</button>
            </div>
          </div>
        </div>
      </div>

      <div class="main">
        <div class="container">
          <h1 class="mb-6">Dashboard</h1>

          <div class="grid grid-4 mb-6">
            <div class="card" onclick="window.location.href='/pos'" style="cursor: pointer;">
              <div class="card-content">
                <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                  <div style="width: 48px; height: 48px; background: var(--primary); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <span class="material-icons" style="color: white;">point_of_sale</span>
                  </div>
                  <div>
                    <h3 style="margin: 0;">Ponto de Venda</h3>
                    <p class="text-muted" style="margin: 0; font-size: 14px;">R$ 2.450,00 hoje</p>
                  </div>
                </div>
                <p class="text-muted">Sistema completo de vendas com interface intuitiva</p>
              </div>
            </div>

            <div class="card" onclick="window.location.href='/kitchen'" style="cursor: pointer;">
              <div class="card-content">
                <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                  <div style="width: 48px; height: 48px; background: var(--secondary); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <span class="material-icons" style="color: var(--dark);">restaurant</span>
                  </div>
                  <div>
                    <h3 style="margin: 0;">Sistema de Cozinha</h3>
                    <p class="text-muted" style="margin: 0; font-size: 14px;">3 pedidos ativos</p>
                  </div>
                </div>
                <p class="text-muted">Display em tempo real de pedidos</p>
              </div>
            </div>

            <div class="card" onclick="window.location.href='/products'" style="cursor: pointer;">
              <div class="card-content">
                <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                  <div style="width: 48px; height: 48px; background: var(--accent); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <span class="material-icons" style="color: var(--dark);">inventory_2</span>
                  </div>
                  <div>
                    <h3 style="margin: 0;">Gestão de Produtos</h3>
                    <p class="text-muted" style="margin: 0; font-size: 14px;">${mockData.products.length} produtos</p>
                  </div>
                </div>
                <p class="text-muted">Cadastro e controle de produtos</p>
              </div>
            </div>

            <div class="card" onclick="window.location.href='/reports'" style="cursor: pointer;">
              <div class="card-content">
                <div style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                  <div style="width: 48px; height: 48px; background: var(--dark); border-radius: 12px; display: flex; align-items: center; justify-content: center;">
                    <span class="material-icons" style="color: white;">analytics</span>
                  </div>
                  <div>
                    <h3 style="margin: 0;">Relatórios</h3>
                    <p class="text-muted" style="margin: 0; font-size: 14px;">15 vendas hoje</p>
                  </div>
                </div>
                <p class="text-muted">Análises e relatórios de vendas</p>
              </div>
            </div>
          </div>

          <div class="grid grid-2">
            <div class="card">
              <div class="card-header">
                <h3>Vendas Recentes</h3>
              </div>
              <div class="card-content">
                <div style="display: flex; flex-direction: column; gap: 12px;">
                  ${mockData.orders.map(order => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #F9FAFB; border-radius: 8px;">
                      <div>
                        <div style="font-weight: 500;">${order.number}</div>
                        <div class="text-muted" style="font-size: 14px;">${new Date(order.created_at).toLocaleTimeString()}</div>
                      </div>
                      <div style="text-align: right;">
                        <div style="font-weight: 600; color: var(--primary);">R$ ${order.total.toFixed(2)}</div>
                        <span class="status-badge status-${order.status}">
                          ${order.status === 'pending' ? 'Pendente' : order.status === 'preparing' ? 'Preparando' : 'Pronto'}
                        </span>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>

            <div class="card">
              <div class="card-header">
                <h3>Resumo do Dia</h3>
              </div>
              <div class="card-content">
                <div style="display: flex; flex-direction: column; gap: 16px;">
                  <div style="display: flex; justify-content: space-between;">
                    <span>Total de Vendas:</span>
                    <span style="font-weight: 600;">R$ ${mockData.orders.reduce((sum, order) => sum + order.total, 0).toFixed(2)}</span>
                  </div>
                  <div style="display: flex; justify-content: space-between;">
                    <span>Pedidos:</span>
                    <span style="font-weight: 600;">${mockData.orders.length}</span>
                  </div>
                  <div style="display: flex; justify-content: space-between;">
                    <span>Ticket Médio:</span>
                    <span style="font-weight: 600;">R$ ${(mockData.orders.reduce((sum, order) => sum + order.total, 0) / mockData.orders.length).toFixed(2)}</span>
                  </div>
                  <hr style="border: none; border-top: 1px solid #E5E7EB; margin: 8px 0;">
                  <div style="display: flex; flex-direction: column; gap: 8px;">
                    <span class="status-badge" style="background: #D1FAE5; color: #065F46;">Sistema Online</span>
                    <span class="status-badge" style="background: #D1FAE5; color: #065F46;">Banco de Dados OK</span>
                    <span class="status-badge" style="background: #D1FAE5; color: #065F46;">Sincronização Ativa</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <script>
        function logout() {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/';
        }

        // Verificar se está logado
        if (!localStorage.getItem('token')) {
          window.location.href = '/';
        }
      </script>
    `;

    const html = generateHTML('Dashboard', content);
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  }

  // PDV (Ponto de Venda)
  else if (pathname === '/pos' && method === 'GET') {
    const content = getPOSPage(mockData);
    const html = generateHTML('PDV', content);
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  }

  // Cozinha
  else if (pathname === '/kitchen' && method === 'GET') {
    const content = getKitchenPage(mockData);
    const html = generateHTML('Cozinha', content);
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  }

  // Produtos
  else if (pathname === '/products' && method === 'GET') {
    const content = `
      <div class="header">
        <div class="container">
          <div class="header-content">
            <div class="logo">
              <span class="material-icons icon-lg">inventory_2</span>
              Adib PDV - Gestão de Produtos
            </div>
            <div class="nav">
              <a href="/dashboard" class="btn btn-outline">Dashboard</a>
              <a href="/pos" class="btn btn-outline">PDV</a>
              <a href="/kitchen" class="btn btn-outline">Cozinha</a>
              <a href="/products" class="btn btn-outline">Produtos</a>
              <button onclick="logout()" class="btn btn-outline">Sair</button>
            </div>
          </div>
        </div>
      </div>

      <div class="main">
        <div class="container">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 32px;">
            <h1 style="margin: 0;">Gestão de Produtos</h1>
            <button onclick="openAddModal()" class="btn btn-primary">
              <span class="material-icons icon">add</span>
              Novo Produto
            </button>
          </div>

          <div class="card">
            <div class="card-content">
              <div style="overflow-x: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                  <thead>
                    <tr style="border-bottom: 2px solid #E5E7EB;">
                      <th style="padding: 12px; text-align: left; font-weight: 600;">ID</th>
                      <th style="padding: 12px; text-align: left; font-weight: 600;">Nome</th>
                      <th style="padding: 12px; text-align: left; font-weight: 600;">Categoria</th>
                      <th style="padding: 12px; text-align: left; font-weight: 600;">Preço</th>
                      <th style="padding: 12px; text-align: left; font-weight: 600;">Status</th>
                      <th style="padding: 12px; text-align: left; font-weight: 600;">Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    ${mockData.products.map(product => `
                      <tr style="border-bottom: 1px solid #E5E7EB;">
                        <td style="padding: 12px;">${product.id}</td>
                        <td style="padding: 12px; font-weight: 500;">${product.name}</td>
                        <td style="padding: 12px;">${product.category}</td>
                        <td style="padding: 12px; color: var(--primary); font-weight: 600;">R$ ${product.price.toFixed(2)}</td>
                        <td style="padding: 12px;">
                          <span class="status-badge" style="background: ${product.active ? '#D1FAE5' : '#FEE2E2'}; color: ${product.active ? '#065F46' : '#991B1B'};">
                            ${product.active ? 'Ativo' : 'Inativo'}
                          </span>
                        </td>
                        <td style="padding: 12px;">
                          <div style="display: flex; gap: 8px;">
                            <button onclick="editProduct(${product.id})" class="btn" style="padding: 6px 12px; background: var(--primary); color: white;">
                              <span class="material-icons" style="font-size: 16px;">edit</span>
                            </button>
                            <button onclick="toggleProduct(${product.id})" class="btn" style="padding: 6px 12px; background: ${product.active ? '#FFBA08' : '#37ECC8'}; color: var(--dark);">
                              <span class="material-icons" style="font-size: 16px;">${product.active ? 'visibility_off' : 'visibility'}</span>
                            </button>
                          </div>
                        </td>
                      </tr>
                    `).join('')}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Modal para adicionar/editar produto -->
      <div id="productModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="background: white; margin: 5% auto; padding: 30px; width: 500px; border-radius: 12px; position: relative;">
          <span onclick="closeModal()" style="position: absolute; top: 15px; right: 20px; font-size: 24px; cursor: pointer;">&times;</span>
          <h2 id="modalTitle">Novo Produto</h2>
          <form id="productForm">
            <div class="form-group">
              <label class="label">Nome:</label>
              <input type="text" class="input" id="productName" required>
            </div>
            <div class="form-group">
              <label class="label">Categoria:</label>
              <select class="input" id="productCategory" required>
                <option value="">Selecione uma categoria</option>
                <option value="Hambúrgueres">Hambúrgueres</option>
                <option value="Pizzas">Pizzas</option>
                <option value="Bebidas">Bebidas</option>
                <option value="Acompanhamentos">Acompanhamentos</option>
                <option value="Sobremesas">Sobremesas</option>
              </select>
            </div>
            <div class="form-group">
              <label class="label">Preço:</label>
              <input type="number" class="input" id="productPrice" step="0.01" min="0" required>
            </div>
            <div class="form-group">
              <label class="label">Status:</label>
              <select class="input" id="productStatus" required>
                <option value="true">Ativo</option>
                <option value="false">Inativo</option>
              </select>
            </div>
            <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
              <button type="button" class="btn btn-outline" onclick="closeModal()" style="color: var(--dark); border-color: #E5E7EB;">Cancelar</button>
              <button type="submit" class="btn btn-primary">Salvar</button>
            </div>
          </form>
        </div>
      </div>

      <script>
        let editingProductId = null;

        function openAddModal() {
          editingProductId = null;
          document.getElementById('modalTitle').textContent = 'Novo Produto';
          document.getElementById('productForm').reset();
          document.getElementById('productModal').style.display = 'block';
        }

        async function editProduct(id) {
          editingProductId = id;
          document.getElementById('modalTitle').textContent = 'Editar Produto';

          // Buscar dados do produto
          try {
            const response = await fetch('/api/products');
            const data = await response.json();
            const product = data.data.find(p => p.id === id);

            if (product) {
              document.getElementById('productName').value = product.name;
              document.getElementById('productCategory').value = product.category;
              document.getElementById('productPrice').value = product.price;
              document.getElementById('productStatus').value = product.active.toString();
              document.getElementById('productModal').style.display = 'block';
            }
          } catch (error) {
            alert('Erro ao carregar dados do produto');
          }
        }

        async function toggleProduct(id) {
          try {
            const response = await fetch(\`/api/products/\${id}/toggle\`, {
              method: 'PATCH'
            });
            const data = await response.json();

            if (data.success) {
              alert(data.message);
              window.location.reload();
            } else {
              alert('Erro: ' + data.error);
            }
          } catch (error) {
            alert('Erro de conexão: ' + error.message);
          }
        }

        function closeModal() {
          document.getElementById('productModal').style.display = 'none';
        }

        // Salvar produto
        document.getElementById('productForm').addEventListener('submit', async function(e) {
          e.preventDefault();

          const name = document.getElementById('productName').value;
          const category = document.getElementById('productCategory').value;
          const price = parseFloat(document.getElementById('productPrice').value);
          const active = document.getElementById('productStatus').value === 'true';

          const productData = { name, category, price, active };

          try {
            let response;
            if (editingProductId) {
              response = await fetch(\`/api/products/\${editingProductId}\`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(productData)
              });
            } else {
              response = await fetch('/api/products', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(productData)
              });
            }

            const data = await response.json();

            if (data.success) {
              alert(data.message);
              closeModal();
              window.location.reload();
            } else {
              alert('Erro: ' + data.error);
            }
          } catch (error) {
            alert('Erro de conexão: ' + error.message);
          }
        });

        function logout() {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/';
        }

        // Verificar se está logado
        if (!localStorage.getItem('token')) {
          window.location.href = '/';
        }
      </script>
    `;

    const html = generateHTML('Produtos', content);
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  }

  // Relatórios
  else if (pathname === '/reports' && method === 'GET') {
    const content = `
      <div class="header">
        <div class="container">
          <div class="header-content">
            <div class="logo">
              <span class="material-icons icon-lg">analytics</span>
              Adib PDV - Relatórios e Analytics
            </div>
            <div class="nav">
              <a href="/dashboard" class="btn btn-outline">Dashboard</a>
              <a href="/pos" class="btn btn-outline">PDV</a>
              <a href="/kitchen" class="btn btn-outline">Cozinha</a>
              <a href="/products" class="btn btn-outline">Produtos</a>
              <button onclick="logout()" class="btn btn-outline">Sair</button>
            </div>
          </div>
        </div>
      </div>

      <div class="main">
        <div class="container">
          <h1 class="mb-6">Relatórios e Analytics</h1>

          <!-- Cards de Resumo -->
          <div class="grid grid-4 mb-6">
            <div class="card">
              <div class="card-content text-center">
                <div style="width: 48px; height: 48px; background: var(--primary); border-radius: 12px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center;">
                  <span class="material-icons" style="color: white;">trending_up</span>
                </div>
                <h3 style="margin: 0 0 8px 0;">Total de Vendas</h3>
                <p style="margin: 0; color: var(--primary); font-weight: 700; font-size: 24px;" id="totalSales">R$ 0,00</p>
                <p class="text-muted" style="margin: 0; font-size: 14px;">Últimos 5 dias</p>
              </div>
            </div>

            <div class="card">
              <div class="card-content text-center">
                <div style="width: 48px; height: 48px; background: var(--secondary); border-radius: 12px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center;">
                  <span class="material-icons" style="color: var(--dark);">receipt_long</span>
                </div>
                <h3 style="margin: 0 0 8px 0;">Total de Pedidos</h3>
                <p style="margin: 0; color: var(--secondary); font-weight: 700; font-size: 24px;" id="totalOrders">0</p>
                <p class="text-muted" style="margin: 0; font-size: 14px;">Últimos 5 dias</p>
              </div>
            </div>

            <div class="card">
              <div class="card-content text-center">
                <div style="width: 48px; height: 48px; background: var(--accent); border-radius: 12px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center;">
                  <span class="material-icons" style="color: var(--dark);">payments</span>
                </div>
                <h3 style="margin: 0 0 8px 0;">Ticket Médio</h3>
                <p style="margin: 0; color: var(--accent); font-weight: 700; font-size: 24px;" id="averageTicket">R$ 0,00</p>
                <p class="text-muted" style="margin: 0; font-size: 14px;">Por pedido</p>
              </div>
            </div>

            <div class="card">
              <div class="card-content text-center">
                <div style="width: 48px; height: 48px; background: var(--dark); border-radius: 12px; margin: 0 auto 12px; display: flex; align-items: center; justify-content: center;">
                  <span class="material-icons" style="color: white;">today</span>
                </div>
                <h3 style="margin: 0 0 8px 0;">Vendas Hoje</h3>
                <p style="margin: 0; color: var(--dark); font-weight: 700; font-size: 24px;">R$ 1.250,00</p>
                <p class="text-muted" style="margin: 0; font-size: 14px;">15 pedidos</p>
              </div>
            </div>
          </div>

          <div class="grid grid-2">
            <!-- Vendas por Período -->
            <div class="card">
              <div class="card-header">
                <h3>Vendas por Período</h3>
              </div>
              <div class="card-content">
                <div style="overflow-x: auto;">
                  <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                      <tr style="border-bottom: 2px solid #E5E7EB;">
                        <th style="padding: 12px; text-align: left; font-weight: 600;">Data</th>
                        <th style="padding: 12px; text-align: left; font-weight: 600;">Pedidos</th>
                        <th style="padding: 12px; text-align: left; font-weight: 600;">Total</th>
                      </tr>
                    </thead>
                    <tbody id="salesTable">
                      <!-- Dados serão carregados aqui -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Produtos Mais Vendidos -->
            <div class="card">
              <div class="card-header">
                <h3>Produtos Mais Vendidos</h3>
              </div>
              <div class="card-content">
                <div style="display: flex; flex-direction: column; gap: 12px;">
                  ${mockData.products.slice(0, 5).map((product, index) => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 12px; background: #F9FAFB; border-radius: 8px;">
                      <div style="display: flex; align-items: center; gap: 12px;">
                        <div style="width: 32px; height: 32px; background: var(--primary); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                          ${index + 1}
                        </div>
                        <div>
                          <div style="font-weight: 500;">${product.name}</div>
                          <div class="text-muted" style="font-size: 14px;">${product.category}</div>
                        </div>
                      </div>
                      <div style="text-align: right;">
                        <div style="font-weight: 600;">${Math.floor(Math.random() * 50) + 10} vendas</div>
                        <div class="text-muted" style="font-size: 14px;">R$ ${product.price.toFixed(2)}</div>
                      </div>
                    </div>
                  `).join('')}
                </div>
              </div>
            </div>

            <!-- Formas de Pagamento -->
            <div class="card">
              <div class="card-header">
                <h3>Formas de Pagamento</h3>
              </div>
              <div class="card-content">
                <div style="display: flex; flex-direction: column; gap: 16px;">
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 12px;">
                      <span class="material-icons" style="color: var(--primary);">credit_card</span>
                      <span>Cartão de Crédito</span>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-weight: 600;">45%</div>
                      <div class="text-muted" style="font-size: 14px;">R$ 562,50</div>
                    </div>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 12px;">
                      <span class="material-icons" style="color: var(--secondary);">pix</span>
                      <span>PIX</span>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-weight: 600;">30%</div>
                      <div class="text-muted" style="font-size: 14px;">R$ 375,00</div>
                    </div>
                  </div>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 12px;">
                      <span class="material-icons" style="color: var(--accent);">payments</span>
                      <span>Dinheiro</span>
                    </div>
                    <div style="text-align: right;">
                      <div style="font-weight: 600;">25%</div>
                      <div class="text-muted" style="font-size: 14px;">R$ 312,50</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Insights GPT-4 -->
            <div class="card">
              <div class="card-header">
                <h3>Insights Diários (GPT-4)</h3>
              </div>
              <div class="card-content">
                <div style="background: linear-gradient(135deg, #0070F3 0%, #37ECC8 100%); padding: 20px; border-radius: 12px; color: white; margin-bottom: 16px;">
                  <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 12px;">
                    <span class="material-icons">psychology</span>
                    <span style="font-weight: 600;">Análise Inteligente</span>
                  </div>
                  <p style="margin: 0; line-height: 1.5;">
                    "Suas vendas de hambúrgueres aumentaram 15% esta semana. Considere promover acompanhamentos para aumentar o ticket médio."
                  </p>
                </div>
                <button class="btn btn-primary" style="width: 100%;" onclick="generateInsights()">
                  <span class="material-icons icon">auto_awesome</span>
                  Gerar Novos Insights
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <script>
        async function loadReports() {
          try {
            const response = await fetch('/api/reports/sales');
            const data = await response.json();

            if (data.success) {
              // Atualizar cards de resumo
              document.getElementById('totalSales').textContent = 'R$ ' + data.summary.totalSales.toFixed(2);
              document.getElementById('totalOrders').textContent = data.summary.totalOrders;
              document.getElementById('averageTicket').textContent = 'R$ ' + data.summary.averageTicket.toFixed(2);

              // Atualizar tabela de vendas
              const salesTable = document.getElementById('salesTable');
              salesTable.innerHTML = data.data.map(sale => \`
                <tr style="border-bottom: 1px solid #E5E7EB;">
                  <td style="padding: 12px;">\${new Date(sale.date).toLocaleDateString('pt-BR')}</td>
                  <td style="padding: 12px;">\${sale.orders}</td>
                  <td style="padding: 12px; color: var(--primary); font-weight: 600;">R$ \${sale.total.toFixed(2)}</td>
                </tr>
              \`).join('');
            }
          } catch (error) {
            console.error('Erro ao carregar relatórios:', error);
          }
        }

        function generateInsights() {
          alert('Funcionalidade de insights GPT-4 em desenvolvimento!\\n\\nEm breve você terá análises inteligentes automáticas do seu negócio.');
        }

        function logout() {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          window.location.href = '/';
        }

        // Verificar se está logado
        if (!localStorage.getItem('token')) {
          window.location.href = '/';
        }

        // Carregar relatórios ao iniciar
        loadReports();
      </script>
    `;

    const html = generateHTML('Relatórios', content);
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  }

  // Outras rotas retornam 404
  else {
    sendJSON(res, {
      success: false,
      error: 'Endpoint não encontrado',
      message: `${method} ${pathname} não existe`
    }, 404);
  }
});

// Iniciar servidor
server.listen(PORT, () => {
  console.log('\n🚀 Sistema PDV Adib - Design Meta iniciado!');
  console.log(`📡 Servidor rodando em: http://localhost:${PORT}`);
  console.log(`🌐 Interface Web: http://localhost:${PORT}`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/api/health`);
  console.log('\n👤 Credenciais de teste:');
  console.log('   Admin: admin / password');
  console.log('   Caixa: caixa / password');
  console.log('   Cozinha: cozinha / password');
  console.log('\n✨ Sistema com design moderno estilo Meta pronto!');
  console.log('\n🌐 Abra no navegador: http://localhost:' + PORT);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🔄 Encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n🔄 Encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});

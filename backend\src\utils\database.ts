import fs from 'fs';
import path from 'path';
import sqlite3 from 'sqlite3';

let db: sqlite3.Database | null = null;

const DB_PATH = path.join(__dirname, '..', '..', '..', 'database', 'adib.db');

export async function initDatabase(): Promise<sqlite3.Database> {
  if (db) {
    return db;
  }

  try {
    // Verificar se o arquivo do banco existe
    if (!fs.existsSync(DB_PATH)) {
      throw new Error(`Database file not found: ${DB_PATH}`);
    }

    // Abrir conexão com o banco
    db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('❌ Database connection failed:', err);
        throw err;
      }
    });

    // Configurar SQLite para melhor performance
    db.exec(`
      PRAGMA foreign_keys = ON;
      PRAGMA journal_mode = WAL;
      PRAGMA synchronous = NORMAL;
      PRAGMA cache_size = 1000;
      PRAGMA temp_store = MEMORY;
    `);

    console.log('✅ Database connected successfully');
    return db;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

export async function getDatabase(): Promise<sqlite3.Database> {
  if (!db) {
    return await initDatabase();
  }
  return db;
}

export async function closeDatabase(): Promise<void> {
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('Database connection closed');
      }
    });
    db = null;
  }
}

// Função para executar queries com tratamento de erro
export function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T[]> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      database.all(query, params, (err, rows) => {
        if (err) {
          console.error('Query execution failed:', { query, params, error: err });
          reject(err);
        } else {
          resolve(rows as T[]);
        }
      });
    } catch (error) {
      console.error('Database connection failed:', error);
      reject(error);
    }
  });
}

// Função para executar query que retorna um único resultado
export function executeQuerySingle<T = any>(
  query: string,
  params: any[] = []
): Promise<T | undefined> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      database.get(query, params, (err, row) => {
        if (err) {
          console.error('Query execution failed:', { query, params, error: err });
          reject(err);
        } else {
          resolve(row as T);
        }
      });
    } catch (error) {
      console.error('Database connection failed:', error);
      reject(error);
    }
  });
}

// Função para executar INSERT/UPDATE/DELETE
export function executeUpdate(
  query: string,
  params: any[] = []
): Promise<{ lastID?: number; changes: number }> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();
      database.run(query, params, function(err) {
        if (err) {
          console.error('Update execution failed:', { query, params, error: err });
          reject(err);
        } else {
          resolve({
            lastID: this.lastID,
            changes: this.changes || 0,
          });
        }
      });
    } catch (error) {
      console.error('Database connection failed:', error);
      reject(error);
    }
  });
}

// Função para executar transações
export function executeTransaction<T>(
  callback: (db: sqlite3.Database) => Promise<T>
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    try {
      const database = await getDatabase();

      database.exec('BEGIN TRANSACTION', async (err) => {
        if (err) {
          reject(err);
          return;
        }

        try {
          const result = await callback(database);
          database.exec('COMMIT', (commitErr) => {
            if (commitErr) {
              reject(commitErr);
            } else {
              resolve(result);
            }
          });
        } catch (error) {
          database.exec('ROLLBACK', () => {
            console.error('Transaction failed:', error);
            reject(error);
          });
        }
      });
    } catch (error) {
      console.error('Database connection failed:', error);
      reject(error);
    }
  });
}

// Função para verificar se uma tabela existe
export async function tableExists(tableName: string): Promise<boolean> {
  const result = await executeQuerySingle<{ count: number }>(
    "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name=?",
    [tableName]
  );
  return (result?.count || 0) > 0;
}

// Função para obter informações da tabela
export async function getTableInfo(tableName: string): Promise<any[]> {
  return await executeQuery(`PRAGMA table_info(${tableName})`);
}

// Função para backup do banco
export async function backupDatabase(backupPath: string): Promise<void> {
  try {
    // Criar diretório de backup se não existir
    const backupDir = path.dirname(backupPath);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Copiar arquivo do banco
    fs.copyFileSync(DB_PATH, backupPath);
    console.log(`Database backed up to: ${backupPath}`);
  } catch (error) {
    console.error('Backup failed:', error);
    throw error;
  }
}

// Função para restaurar backup
export async function restoreDatabase(backupPath: string): Promise<void> {
  if (!fs.existsSync(backupPath)) {
    throw new Error(`Backup file not found: ${backupPath}`);
  }

  try {
    // Fechar conexão atual
    await closeDatabase();

    // Restaurar arquivo
    fs.copyFileSync(backupPath, DB_PATH);

    // Reconectar
    await initDatabase();

    console.log(`Database restored from: ${backupPath}`);
  } catch (error) {
    console.error('Restore failed:', error);
    throw error;
  }
}

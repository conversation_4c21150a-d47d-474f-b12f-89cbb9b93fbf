{"name": "adib-pdv-backend", "version": "1.0.0", "description": "Backend do Sistema PDV Adib - Node.js + Express + SQLite", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:migrate": "node dist/database/migrate.js", "db:seed": "node dist/database/seed.js", "db:reset": "node dist/database/reset.js", "db:backup": "node dist/database/backup.js"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "morgan": "^1.10.0", "multer": "^2.0.0", "node-cron": "^4.1.0", "socket.io": "^4.8.1", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts"]}, "nodemonConfig": {"watch": ["src"], "ext": "ts", "ignore": ["src/**/*.test.ts"], "exec": "ts-node src/server.ts"}}
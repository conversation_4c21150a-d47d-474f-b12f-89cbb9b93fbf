const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Conectar ao banco de dados
const DB_PATH = path.join(__dirname, '..', 'database', 'adib.db');
let db = null;

const initDatabase = () => {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(DB_PATH)) {
      reject(new Error(`Database file not found: ${DB_PATH}`));
      return;
    }

    db = new sqlite3.Database(DB_PATH, (err) => {
      if (err) {
        console.error('❌ Database connection failed:', err);
        reject(err);
      } else {
        console.log('✅ Database connected successfully');

        // Configurar SQLite
        db.exec(`
          PRAGMA foreign_keys = ON;
          PRAGMA journal_mode = WAL;
          PRAGMA synchronous = NORMAL;
        `);

        resolve(db);
      }
    });
  });
};

// Função para formatar resposta
const formatResponse = (success, data = null, error = null) => ({
  success,
  data,
  error,
  timestamp: new Date().toISOString(),
});

// Função para executar query
const executeQuery = (query, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(query, params, (err, rows) => {
      if (err) {
        console.error('Query error:', { query, params, error: err });
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// Função para executar query única
const executeQuerySingle = (query, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(query, params, (err, row) => {
      if (err) {
        console.error('Query error:', { query, params, error: err });
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

// Função para executar update
const executeUpdate = (query, params = []) => {
  return new Promise((resolve, reject) => {
    db.run(query, params, function(err) {
      if (err) {
        console.error('Update error:', { query, params, error: err });
        reject(err);
      } else {
        resolve({
          lastID: this.lastID,
          changes: this.changes || 0,
        });
      }
    });
  });
};

// Health check
app.get('/api/health', (req, res) => {
  res.status(200).json(formatResponse(true, {
    status: 'healthy',
    version: '1.0.0',
    environment: 'development',
    database: db ? 'connected' : 'disconnected',
  }));
});

// Rota raiz da API
app.get('/api', (req, res) => {
  res.status(200).json(formatResponse(true, {
    message: 'Adib PDV Backend API',
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      health: '/api/health',
      products: '/api/products',
      categories: '/api/products/categories',
    },
  }));
});

// Autenticação
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json(formatResponse(false, null, 'Username e password são obrigatórios'));
    }

    // Buscar usuário no banco
    const user = await executeQuerySingle(
      'SELECT * FROM users WHERE username = ? AND is_active = 1',
      [username]
    );

    if (!user || user.password !== password) {
      return res.status(401).json(formatResponse(false, null, 'Credenciais inválidas'));
    }

    const token = `token_${Date.now()}_${user.id}`;
    const userData = {
      id: user.id,
      username: user.username,
      full_name: user.full_name,
      email: user.email,
      role: user.role,
      is_active: user.is_active,
      created_at: user.created_at,
      updated_at: user.updated_at,
    };

    res.status(200).json(formatResponse(true, {
      user: userData,
      token,
      expires_in: '24h',
    }));
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json(formatResponse(false, null, 'Erro interno do servidor'));
  }
});

app.post('/api/auth/logout', (req, res) => {
  res.status(200).json(formatResponse(true, { message: 'Logout realizado com sucesso' }));
});

// Produtos
app.get('/api/products', async (req, res) => {
  try {
    const { category_id, search, active = 'true' } = req.query;

    let query = `
      SELECT p.*, c.name as category_name, c.color as category_color
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE 1=1
    `;
    const params = [];

    if (active !== undefined) {
      query += ' AND p.active = ?';
      params.push(active === 'true' ? 1 : 0);
    }

    if (category_id) {
      query += ' AND p.category_id = ?';
      params.push(category_id);
    }

    if (search) {
      query += ' AND (p.name LIKE ? OR p.code LIKE ? OR p.barcode LIKE ?)';
      const searchTerm = `%${search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY p.name ASC';

    const products = await executeQuery(query, params);
    res.status(200).json(formatResponse(true, products));
  } catch (error) {
    console.error('Get products error:', error);
    res.status(500).json(formatResponse(false, null, 'Erro ao buscar produtos'));
  }
});

app.get('/api/products/categories', async (req, res) => {
  try {
    const categories = await executeQuery(
      'SELECT * FROM categories WHERE active = 1 ORDER BY name ASC'
    );
    res.status(200).json(formatResponse(true, categories));
  } catch (error) {
    console.error('Get categories error:', error);
    res.status(500).json(formatResponse(false, null, 'Erro ao buscar categorias'));
  }
});

app.post('/api/products', async (req, res) => {
  try {
    const {
      code,
      name,
      description,
      category_id,
      price,
      cost,
      unit,
      barcode,
      stock_control,
      current_stock,
      min_stock,
      max_stock,
      preparation_time,
      active = true,
    } = req.body;

    if (!name || !price || !category_id) {
      return res.status(400).json(formatResponse(false, null, 'Nome, preço e categoria são obrigatórios'));
    }

    const result = await executeUpdate(
      `INSERT INTO products (
        code, name, description, category_id, price, cost, unit, barcode,
        stock_control, current_stock, min_stock, max_stock, preparation_time,
        active, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        code || null,
        name,
        description || null,
        category_id,
        price,
        cost || 0,
        unit || 'UN',
        barcode || null,
        stock_control ? 1 : 0,
        current_stock || 0,
        min_stock || 0,
        max_stock || 0,
        preparation_time || 0,
        active ? 1 : 0,
        new Date().toISOString(),
        new Date().toISOString(),
      ]
    );

    res.status(201).json(formatResponse(true, {
      id: result.lastID,
      message: 'Produto criado com sucesso',
    }));
  } catch (error) {
    console.error('Create product error:', error);
    res.status(500).json(formatResponse(false, null, 'Erro ao criar produto'));
  }
});

app.put('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const existingProduct = await executeQuerySingle('SELECT id FROM products WHERE id = ?', [id]);
    if (!existingProduct) {
      return res.status(404).json(formatResponse(false, null, 'Produto não encontrado'));
    }

    const updateFields = [];
    const updateValues = [];

    const allowedFields = [
      'code', 'name', 'description', 'category_id', 'price', 'cost',
      'unit', 'barcode', 'stock_control', 'current_stock', 'min_stock',
      'max_stock', 'preparation_time', 'active'
    ];

    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(updateData[field]);
      }
    });

    if (updateFields.length === 0) {
      return res.status(400).json(formatResponse(false, null, 'Nenhum campo para atualizar'));
    }

    updateFields.push('updated_at = ?');
    updateValues.push(new Date().toISOString());
    updateValues.push(id);

    await executeUpdate(
      `UPDATE products SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    res.status(200).json(formatResponse(true, { message: 'Produto atualizado com sucesso' }));
  } catch (error) {
    console.error('Update product error:', error);
    res.status(500).json(formatResponse(false, null, 'Erro ao atualizar produto'));
  }
});

app.delete('/api/products/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const existingProduct = await executeQuerySingle('SELECT id FROM products WHERE id = ?', [id]);
    if (!existingProduct) {
      return res.status(404).json(formatResponse(false, null, 'Produto não encontrado'));
    }

    await executeUpdate(
      'UPDATE products SET active = 0, updated_at = ? WHERE id = ?',
      [new Date().toISOString(), id]
    );

    res.status(200).json(formatResponse(true, { message: 'Produto excluído com sucesso' }));
  } catch (error) {
    console.error('Delete product error:', error);
    res.status(500).json(formatResponse(false, null, 'Erro ao excluir produto'));
  }
});

// Middleware para rotas não encontradas
app.use('/api/*', (req, res) => {
  res.status(404).json(formatResponse(false, null, 'Endpoint não encontrado'));
});

// Middleware global de tratamento de erros
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);
  res.status(error.statusCode || 500).json(formatResponse(false, null, error.message || 'Erro interno do servidor'));
});

// Iniciar servidor
const startServer = async () => {
  try {
    await initDatabase();

    app.listen(PORT, () => {
      console.log(`🚀 Server running on http://localhost:${PORT}`);
      console.log(`📚 API Documentation: http://localhost:${PORT}/api`);
      console.log(`🏥 Health Check: http://localhost:${PORT}/api/health`);
      console.log(`🔐 Auth Endpoints: http://localhost:${PORT}/api/auth`);
      console.log(`📦 Products: http://localhost:${PORT}/api/products`);
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n📴 Shutting down server...');
  if (db) {
    db.close((err) => {
      if (err) {
        console.error('Error closing database:', err);
      } else {
        console.log('🗄️  Database connection closed');
      }
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
});
